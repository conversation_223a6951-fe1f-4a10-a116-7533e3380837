# Lignes directrices pour l'agent IA sur le projet SIGMA (v1.2)



# 1. Architecture & Communication Backend

# Le respect de ces principes est CRITIQUE pour la stabilité et la sécurité du projet.

- 1.1. Pour 95% des cas, toute interaction client -> backend doit se faire via une **Firebase Callable Function**.

- 1.2. Toute opération de modification de données critiques (création d'emprunt, mouvement de stock) DOIT impérativement utiliser une **transaction Firestore** (`runTransaction` ou `writeBatch`), car la simultanéité des accès est un risque métier majeur et l'intégrité des données est non-négociable.

- 1.3. Le temps réel côté client doit se faire avec `onSnapshot` et inclure une **pagination** (`startAfter`) pour maîtriser les coûts de lecture.



# 2. Sécurité & Authentification

# La sécurité n'est pas une option.

- 2.1. La gestion des rôles est basée sur les **Custom Claims** de Firebase Auth. Les seuls rôles valides sont : 'admin', 'regisseur', 'utilisateur'.

- 2.2. Toute règle de sécurité Firestore ou Storage doit se baser sur `request.auth.token.role`.

- 2.3. Les Cloud Functions doivent systématiquement **valider le rôle de l'appelant** en début de fonction.



# 3. Conventions de code & Arborescence

# La cohérence du code est la clé de la maintenabilité.

- 3.1. Respecter scrupuleusement l'arborescence définie dans `@docs/Architecture.md`.

- 3.2. La terminologie est standardisée : **"Emprunt"**, **"Module"**, **"Stock"**, **"Livraison"**.

- 3.3. **(Suggestion)** Les messages de commit doivent suivre le format Conventional Commits (ex: `feat:`, `fix:`, `docs:`).



# 4. Assurance Qualité & Tests

# Un code non testé est un code non terminé.

- 4.1. Toute nouvelle Cloud Function ou modification de logique métier significative DOIT être accompagnée de **tests unitaires** utilisant l'Emulator Suite de Firebase.

- 4.2. Le code ne sera considéré comme complet que si les tests associés **réussissent**.



# 5. Anti-Patterns & Interdits

# Ce qu'il ne faut JAMAIS faire.

- 5.1. **Ne jamais stocker de secrets** en clair dans le code. Utiliser le gestionnaire de secrets de GCP/Firebase.

- 5.2. **Ne pas introduire de nouvelles dépendances npm** sans l'avoir mentionné dans le plan d'action initial.

- 5.3. **Ne pas utiliser de requêtes Firestore non limitées** côté client. Toujours utiliser `.limit()`.



# 6. Utilisation des Outils (MCP)

# Utiliser les bons outils pour gagner en efficacité.

- 6.1. Pour les tâches d'analyse ou de manipulation de données dans Firestore, privilégier les outils du **Firebase MCP Server**.



# 7. Style & Format

- 7.1. Privilégier **TypeScript** pour les Cloud Functions (Node 18).

- 7.2. Les réponses doivent être claires. **Explique systématiquement le raisonnement** derrière les choix de code complexes.

- 7.3. **(Suggestion)** Les erreurs dans les Cloud Functions doivent être logguées de manière explicite avec `functions.logger.error()` en incluant des détails pertinents pour le débogage.

