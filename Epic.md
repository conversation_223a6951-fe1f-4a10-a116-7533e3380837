



---



\# Plan Directeur des Épiques SIGMA (v2.0)



Ce document sert de plan de mission central pour le développement de SIGMA. Chaque ligne représente une mission autonome pour un agent IA, en liant l'objectif stratégique aux documents techniques nécessaires à sa réalisation.



---



\## E-1 — Sécurité \& Authentification



\*\*Objectif global\*\*

Mettre en place une authentification Google et une gestion fine des rôles pour protéger l'accès.



\*\*Documents de Référence Clés\*\*



\* `@docs/Permissions.md`

\* `@docs/Architecture\_SIGMA\_v1.2.md`



\*\*Critères de succès (Definition of Done)\*\*



\* Connexion Google OAuth fonctionnelle.

\* Custom Claims (admin, régisseur, utilisateur) injectés via Cloud Function.

\* Règles Firestore/Storage basées sur les rôles.

\* Tests d'accès non autorisé 100% KO.



---



\## E-2 — Flux « Emprunts »



\*\*Objectif global\*\*

Industrialiser le cycle complet des emprunts : création, départ, retour, et génération de PDF.



\*\*Documents de Référence Clés\*\*



\* `@docs/Gestion des Emprunts dans SIGMA Firebase.md`

\* `@docs/Interface\_SIGMA\_v1.0.md`

\* `@docs/DataModel.md`



\*\*Critères de succès\*\*



\* 3 Cloud Functions (création, workflow) déployées et testées (≥90%).

\* Génération PDF des étiquettes en < 3s.

\* Formulaire multi-étapes validé par tests Cypress.



---



\## E-3 — Dashboard \& Alerting



\*\*Objectif global\*\*

Offrir un dashboard temps-réel performant avec des alertes automatiques.



\*\*Documents de Référence Clés\*\*



\* `@docs/Onglet Résumé de SIGMA Firebase.md`

\* `@docs/Interface\_SIGMA\_v1.0.md`



\*\*Critères de succès\*\*



\* Listeners temps-réel (stocks, retards) fonctionnels en UI.

\* Requêtes Firestore optimisées (<100 lectures/sec).

\* Alertes Cloud Monitoring actives.

\* Score Lighthouse performance ≥ 90.



---



\## E-4 — Expérience Stocks \& Livraisons



\*\*Objectif global\*\*

Améliorer la visualisation logistique avec des filtres avancés et une carte interactive.



\*\*Documents de Référence Clés\*\*



\* `@docs/État des Stocks dans SIGMA.md`

\* `@docs/Interface\_SIGMA\_v1.0.md`

\* `@docs/Gestion des Modules dans SIGMA.md`



\*\*Critères de succès\*\*



\* Écran Stocks avec filtres dynamiques (<200ms).

\* Carte Leaflet des livraisons fonctionnelle.

\* Tests UX validés par les régisseurs.



---



\## E-5 — Observabilité \& Performance



\*\*Objectif global\*\*

Instrumenter SIGMA pour détecter les régressions de performance et les coûts anormaux.



\*\*Documents de Référence Clés\*\*



\* `@docs/Architecture\_SIGMA\_v1.2.md`



\*\*Critères de succès\*\*



\* Dashboard Cloud Monitoring complet (CPU, Firestore, coûts).

\* Alertes sur latence et quotas actives.

\* Cold-start moyen des CF < 400ms.



---



\## E-6 — Back-ups \& Continuité



\*\*Objectif global\*\*

Garantir la restauration des données et la continuité de service.



\*\*Documents de Référence Clés\*\*



\* `@docs/Architecture\_SIGMA\_v1.2.md`



\*\*Critères de succès\*\*



\* Script de restauration GCS → Firestore testé mensuellement en staging.

\* Smoke-tests 100% verts après restauration.



---



\## E-7 — Qualité \& Sécurité



\*\*Objectif global\*\*

Renforcer la base de code contre les vulnérabilités et garantir la conformité.



\*\*Documents de Référence Clés\*\*



\* `@docs/Permissions.md`

\* `@docs/Architecture\_SIGMA\_v1.2.md`



\*\*Critères de succès\*\*



\* Audit de sécurité (type OWASP) complété.

\* Règles de Storage revues.

\* Accessibilité WCAG AA validée.



---



\## E-8 — Pré-production \& Release



\*\*Objectif global\*\*

Verrouiller le périmètre fonctionnel, tester le rollback et préparer la v1.0.



\*\*Documents de Référence Clés\*\*



\* `@docs/Plan de Développement Détaillé - Application SIGMA.md`



\*\*Critères de succès\*\*



\* Checklist pré-production signée.

\* Procédure de rollback automatisée et testée.

\* Tag v1.0.0 créé sur la branche main.



---



\## E-9 — Gouvernance des Données



\*\*Objectif global\*\*

Stabiliser le modèle de données et la documentation technique.



\*\*Documents de Référence Clés\*\*



\* `@docs/DataModel.md`



\*\*Critères de succès\*\*



\* DataModel.md gelé en v0.9 et validé.

\* Génération automatique des schémas (Zod) en place.

\* Taux de complétude de la documentation > 95%.



---



\## E-10 — Vision Produit \& Feedback



\*\*Objectif global\*\*

Aligner la roadmap sur les retours utilisateurs pour les versions futures.



\*\*Documents de Référence Clés\*\*



\* N/A (Tâche de Product Management)



\*\*Critères de succès\*\*



\* Atelier story-mapping terminé.

\* Retours utilisateurs consolidés.

\* Roadmap v1.1 publiée et validée.



---

