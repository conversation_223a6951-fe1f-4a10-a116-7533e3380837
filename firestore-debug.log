Aug 04, 2025 12:28:45 AM com.google.cloud.datastore.emulator.firestore.websocket.WebSocketServer start
INFO: Started WebSocket server on ws://127.0.0.1:9150
API endpoint: http://127.0.0.1:8080
If you are using a library that supports the FIRESTORE_EMULATOR_HOST environment variable, run:

   export FIRESTORE_EMULATOR_HOST=127.0.0.1:8080

If you are running a Firestore in Datastore Mode project, run:

   export DATASTORE_EMULATOR_HOST=127.0.0.1:8080

Note: Support for Datastore Mode is in preview. If you encounter any bugs please file at https://github.com/firebase/firebase-tools/issues.
Dev App Server is now running.

Aug 04, 2025 12:29:26 AM io.gapi.emulators.netty.HttpVersionRoutingHandler channelRead
INFO: Detected non-HTTP/2 connection.
Aug 04, 2025 12:29:27 AM io.gapi.emulators.netty.HttpVersionRoutingHandler channelRead
INFO: Detected HTTP/2 connection.
Multiple projectIds are not recommended in single project mode. Requested project ID sigma-test-project, but the emulator is configured for sigma-nova. To opt-out of single project mode add/set the '"singleProjectMode": false' property in the firebase.json emulators config.
Multiple projectIds are not recommended in single project mode. Requested project ID sigma-test-project, but the emulator is configured for sigma-nova. To opt-out of single project mode add/set the '"singleProjectMode": false' property in the firebase.json emulators config.
Multiple projectIds are not recommended in single project mode. Requested project ID sigma-test-project, but the emulator is configured for sigma-nova. To opt-out of single project mode add/set the '"singleProjectMode": false' property in the firebase.json emulators config.
Multiple projectIds are not recommended in single project mode. Requested project ID sigma-test-project, but the emulator is configured for sigma-nova. To opt-out of single project mode add/set the '"singleProjectMode": false' property in the firebase.json emulators config.
Multiple projectIds are not recommended in single project mode. Requested project ID sigma-test-project, but the emulator is configured for sigma-nova. To opt-out of single project mode add/set the '"singleProjectMode": false' property in the firebase.json emulators config.
Multiple projectIds are not recommended in single project mode. Requested project ID sigma-test-project, but the emulator is configured for sigma-nova. To opt-out of single project mode add/set the '"singleProjectMode": false' property in the firebase.json emulators config.
Multiple projectIds are not recommended in single project mode. Requested project ID sigma-test-project, but the emulator is configured for sigma-nova. To opt-out of single project mode add/set the '"singleProjectMode": false' property in the firebase.json emulators config.
Multiple projectIds are not recommended in single project mode. Requested project ID sigma-test-project, but the emulator is configured for sigma-nova. To opt-out of single project mode add/set the '"singleProjectMode": false' property in the firebase.json emulators config.
Multiple projectIds are not recommended in single project mode. Requested project ID sigma-test-project, but the emulator is configured for sigma-nova. To opt-out of single project mode add/set the '"singleProjectMode": false' property in the firebase.json emulators config.
Multiple projectIds are not recommended in single project mode. Requested project ID sigma-test-project, but the emulator is configured for sigma-nova. To opt-out of single project mode add/set the '"singleProjectMode": false' property in the firebase.json emulators config.
Multiple projectIds are not recommended in single project mode. Requested project ID sigma-test-project, but the emulator is configured for sigma-nova. To opt-out of single project mode add/set the '"singleProjectMode": false' property in the firebase.json emulators config.
Multiple projectIds are not recommended in single project mode. Requested project ID sigma-test-project, but the emulator is configured for sigma-nova. To opt-out of single project mode add/set the '"singleProjectMode": false' property in the firebase.json emulators config.
Multiple projectIds are not recommended in single project mode. Requested project ID sigma-test-project, but the emulator is configured for sigma-nova. To opt-out of single project mode add/set the '"singleProjectMode": false' property in the firebase.json emulators config.
Multiple projectIds are not recommended in single project mode. Requested project ID sigma-test-project, but the emulator is configured for sigma-nova. To opt-out of single project mode add/set the '"singleProjectMode": false' property in the firebase.json emulators config.
Multiple projectIds are not recommended in single project mode. Requested project ID sigma-test-project, but the emulator is configured for sigma-nova. To opt-out of single project mode add/set the '"singleProjectMode": false' property in the firebase.json emulators config.
Multiple projectIds are not recommended in single project mode. Requested project ID sigma-test-project, but the emulator is configured for sigma-nova. To opt-out of single project mode add/set the '"singleProjectMode": false' property in the firebase.json emulators config.
