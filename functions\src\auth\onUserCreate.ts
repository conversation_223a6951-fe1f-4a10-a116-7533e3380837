/**
 * Cloud Function trigger pour la création automatique d'utilisateurs
 * Assigne le rôle par défaut et initialise le profil utilisateur
 */

import { onUserCreated } from 'firebase-functions/v2/identity';
import { getAuth } from 'firebase-admin/auth';
import { getFirestore } from 'firebase-admin/firestore';
import * as logger from 'firebase-functions/logger';

/**
 * Trigger automatique lors de la création d'un utilisateur
 * 
 * Actions effectuées:
 * - Assignation du rôle 'utilisateur' par défaut via Custom Claims
 * - Création du document utilisateur dans Firestore
 * - Logging pour audit et monitoring
 * 
 * Sécurité:
 * - Transaction Firestore pour cohérence
 * - Gestion d'erreurs robuste
 * - Logging complet pour audit
 */
export const onUserCreate = onUserCreated(
  {
    region: 'europe-west1', // Région européenne pour conformité RGPD
    maxInstances: 10, // Permettre plus d'instances pour les pics d'inscription
    memory: '256MiB',
    timeoutSeconds: 60 // Plus de temps pour les opérations de création
  },
  async (event) => {
    const startTime = Date.now();
    const user = event.data;
    
    try {
      logger.info('👤 Nouveau utilisateur créé', {
        uid: user.uid,
        email: user.email,
        displayName: user.displayName,
        provider: user.providerData?.[0]?.providerId
      });

      // 1. Assignation du rôle par défaut via Custom Claims
      const auth = getAuth();
      const defaultRole = 'utilisateur';
      const customClaims = {
        role: defaultRole,
        roleAssignedAt: new Date().toISOString(),
        roleAssignedBy: 'system', // Assigné automatiquement par le système
        accountCreatedAt: new Date().toISOString()
      };

      await auth.setCustomUserClaims(user.uid, customClaims);
      
      logger.info('✅ Custom Claims assignés', {
        uid: user.uid,
        role: defaultRole
      });

      // 2. Création du document utilisateur dans Firestore
      const db = getFirestore();
      
      // Utiliser une transaction pour garantir la cohérence
      await db.runTransaction(async (transaction) => {
        const userRef = db.collection('users').doc(user.uid);
        
        // Vérifier si le document existe déjà (cas rare mais possible)
        const existingDoc = await transaction.get(userRef);
        if (existingDoc.exists) {
          logger.warn('⚠️ Document utilisateur existe déjà', {
            uid: user.uid,
            email: user.email
          });
          return;
        }

        // Créer le document utilisateur
        const userData = {
          uid: user.uid,
          email: user.email || '',
          displayName: user.displayName || '',
          photoURL: user.photoURL || '',
          role: defaultRole,
          
          // Métadonnées de création
          createdAt: new Date(),
          createdBy: 'system',
          updatedAt: new Date(),
          updatedBy: 'system',
          
          // Historique des rôles
          roleHistory: [
            {
              previousRole: null,
              newRole: defaultRole,
              changedAt: new Date(),
              changedBy: 'system',
              changedByEmail: 'system',
              reason: 'Création automatique du compte'
            }
          ],
          
          // Informations du provider
          provider: user.providerData?.[0]?.providerId || 'unknown',
          
          // Statut du compte
          isActive: true,
          emailVerified: user.emailVerified || false,
          
          // Préférences par défaut
          preferences: {
            language: 'fr',
            notifications: {
              email: true,
              browser: true
            },
            theme: 'light'
          },
          
          // Statistiques d'utilisation (initialisées à zéro)
          stats: {
            loginCount: 0,
            lastLoginAt: null,
            empruntsCount: 0,
            lastActivityAt: new Date()
          }
        };

        transaction.set(userRef, userData);
        
        logger.info('✅ Document utilisateur créé dans Firestore', {
          uid: user.uid,
          email: user.email,
          role: defaultRole
        });
      });

      // 3. Logging de succès avec métriques
      const executionTime = Date.now() - startTime;
      logger.info('🎉 Initialisation utilisateur terminée avec succès', {
        uid: user.uid,
        email: user.email,
        role: defaultRole,
        executionTimeMs: executionTime,
        provider: user.providerData?.[0]?.providerId
      });

      // 4. Optionnel: Envoyer un email de bienvenue (à implémenter plus tard)
      // await sendWelcomeEmail(user.email, user.displayName);

    } catch (error) {
      const executionTime = Date.now() - startTime;
      
      logger.error('❌ Erreur lors de l\'initialisation de l\'utilisateur', {
        uid: user.uid,
        email: user.email,
        error: error instanceof Error ? error.message : String(error),
        stack: error instanceof Error ? error.stack : undefined,
        executionTimeMs: executionTime
      });

      // Ne pas faire échouer la création du compte même si l'initialisation échoue
      // L'utilisateur pourra toujours se connecter et les données seront créées plus tard
      
      // Optionnel: Envoyer une alerte aux administrateurs
      // await notifyAdminsOfUserCreationError(user.uid, error);
    }
  }
);

/**
 * Fonction utilitaire pour valider les données utilisateur
 */
function validateUserData(user: any): boolean {
  return !!(user.uid && user.email);
}

/**
 * Fonction utilitaire pour nettoyer les données utilisateur
 */
function sanitizeUserData(user: any) {
  return {
    uid: user.uid?.trim(),
    email: user.email?.toLowerCase()?.trim(),
    displayName: user.displayName?.trim() || '',
    photoURL: user.photoURL?.trim() || ''
  };
}

/**
 * Fonction utilitaire pour générer les préférences par défaut
 */
function getDefaultPreferences() {
  return {
    language: 'fr',
    notifications: {
      email: true,
      browser: true,
      emprunts: true,
      stocks: false,
      livraisons: false
    },
    theme: 'light',
    dashboard: {
      showWelcome: true,
      defaultView: 'overview'
    }
  };
}

/**
 * Fonction utilitaire pour générer les statistiques initiales
 */
function getInitialStats() {
  return {
    loginCount: 0,
    lastLoginAt: null,
    empruntsCount: 0,
    empruntsActiveCount: 0,
    lastActivityAt: new Date(),
    accountCreatedAt: new Date()
  };
}
