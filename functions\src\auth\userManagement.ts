/**
 * Gestion des utilisateurs côté serveur
 * Fonctions pour lister, récupérer et mettre à jour les profils utilisateurs
 */

import { onCall, HttpsError } from 'firebase-functions/v2/https';
import { getAuth } from 'firebase-admin/auth';
import { getFirestore, FieldValue } from 'firebase-admin/firestore';
import * as logger from 'firebase-functions/logger';
import { z } from 'zod';
import { requireRole, requireAnyRole, logSuccessfulAccess, validateAndSanitizeInput, AuthError, AuthorizationError } from '../utils/auth';

// Schémas de validation
const GetUserRoleSchema = z.object({
  userId: z.string().min(1, 'ID utilisateur requis')
});

const UpdateUserProfileSchema = z.object({
  userId: z.string().min(1, 'ID utilisateur requis'),
  displayName: z.string().optional(),
  preferences: z.object({
    language: z.enum(['fr', 'en']).optional(),
    theme: z.enum(['light', 'dark']).optional(),
    notifications: z.object({
      email: z.boolean().optional(),
      browser: z.boolean().optional(),
      emprunts: z.boolean().optional(),
      stocks: z.boolean().optional(),
      livraisons: z.boolean().optional()
    }).optional(),
    dashboard: z.object({
      showWelcome: z.boolean().optional(),
      defaultView: z.enum(['overview', 'emprunts', 'stocks']).optional()
    }).optional()
  }).optional()
});

const ListUsersSchema = z.object({
  limit: z.number().min(1).max(100).optional().default(20),
  startAfter: z.string().optional(), // Pour la pagination
  role: z.enum(['admin', 'regisseur', 'utilisateur']).optional(),
  searchTerm: z.string().optional() // Pour rechercher par email/nom
});

type GetUserRoleData = z.infer<typeof GetUserRoleSchema>;
type UpdateUserProfileData = z.infer<typeof UpdateUserProfileSchema>;
type ListUsersData = z.infer<typeof ListUsersSchema>;

/**
 * Récupérer le rôle et les informations d'un utilisateur
 */
export const getUserRole = onCall<GetUserRoleData>(
  {
    region: 'europe-west1',
    maxInstances: 10,
    memory: '256MiB',
    timeoutSeconds: 30
  },
  async (request) => {
    try {
      // Vérification de l'authentification
      if (!request.auth) {
        throw new HttpsError('unauthenticated', 'Authentification requise');
      }

      const callerId = request.auth.uid;
      const callerRole = request.auth.token.role;

      // Validation des données
      const { userId } = GetUserRoleSchema.parse(request.data);

      // Vérification des permissions
      // Un utilisateur peut voir ses propres infos, les admins et régisseurs peuvent voir tous les utilisateurs
      if (callerId !== userId && !['admin', 'regisseur'].includes(callerRole)) {
        throw new HttpsError('permission-denied', 'Permissions insuffisantes');
      }

      // Récupération des informations utilisateur
      const auth = getAuth();
      const db = getFirestore();

      const [authUser, firestoreUser] = await Promise.all([
        auth.getUser(userId).catch(() => null),
        db.collection('users').doc(userId).get()
      ]);

      if (!authUser) {
        throw new HttpsError('not-found', 'Utilisateur introuvable');
      }

      const customClaims = authUser.customClaims || {};
      const firestoreData = firestoreUser.exists ? firestoreUser.data() : null;

      return {
        uid: authUser.uid,
        email: authUser.email,
        displayName: authUser.displayName,
        photoURL: authUser.photoURL,
        emailVerified: authUser.emailVerified,
        role: customClaims.role || 'utilisateur',
        roleAssignedAt: customClaims.roleAssignedAt,
        roleAssignedBy: customClaims.roleAssignedBy,
        createdAt: authUser.metadata.creationTime,
        lastSignInTime: authUser.metadata.lastSignInTime,
        isActive: firestoreData?.isActive ?? true,
        preferences: firestoreData?.preferences || {},
        stats: firestoreData?.stats || {}
      };

    } catch (error) {
      if (error instanceof HttpsError) throw error;
      
      logger.error('❌ Erreur dans getUserRole', {
        error: error instanceof Error ? error.message : String(error),
        callerId: request.auth?.uid
      });
      
      throw new HttpsError('internal', 'Erreur interne du serveur');
    }
  }
);

/**
 * Lister les utilisateurs avec pagination et filtres
 */
export const listUsers = onCall<ListUsersData>(
  {
    region: 'europe-west1',
    maxInstances: 5,
    memory: '512MiB',
    timeoutSeconds: 60
  },
  async (request) => {
    try {
      // Vérification de l'authentification
      if (!request.auth) {
        throw new HttpsError('unauthenticated', 'Authentification requise');
      }

      const callerRole = request.auth.token.role;

      // Seuls les admins et régisseurs peuvent lister les utilisateurs
      if (!['admin', 'regisseur'].includes(callerRole)) {
        throw new HttpsError('permission-denied', 'Permissions insuffisantes');
      }

      // Validation des données
      const { limit, startAfter, role, searchTerm } = ListUsersSchema.parse(request.data);

      const db = getFirestore();
      let query = db.collection('users').orderBy('createdAt', 'desc');

      // Filtrage par rôle
      if (role) {
        query = query.where('role', '==', role);
      }

      // Pagination
      if (startAfter) {
        const startAfterDoc = await db.collection('users').doc(startAfter).get();
        if (startAfterDoc.exists) {
          query = query.startAfter(startAfterDoc);
        }
      }

      // Limitation
      query = query.limit(limit);

      const snapshot = await query.get();
      const users = [];

      for (const doc of snapshot.docs) {
        const userData = doc.data();
        
        // Filtrage par terme de recherche (côté serveur pour plus de sécurité)
        if (searchTerm) {
          const searchLower = searchTerm.toLowerCase();
          const emailMatch = userData.email?.toLowerCase().includes(searchLower);
          const nameMatch = userData.displayName?.toLowerCase().includes(searchLower);
          
          if (!emailMatch && !nameMatch) {
            continue;
          }
        }

        users.push({
          uid: doc.id,
          email: userData.email,
          displayName: userData.displayName,
          role: userData.role,
          isActive: userData.isActive,
          createdAt: userData.createdAt,
          lastActivityAt: userData.stats?.lastActivityAt,
          empruntsCount: userData.stats?.empruntsCount || 0
        });
      }

      return {
        users,
        hasMore: snapshot.size === limit,
        lastUserId: snapshot.docs[snapshot.docs.length - 1]?.id
      };

    } catch (error) {
      if (error instanceof HttpsError) throw error;
      
      logger.error('❌ Erreur dans listUsers', {
        error: error instanceof Error ? error.message : String(error),
        callerId: request.auth?.uid
      });
      
      throw new HttpsError('internal', 'Erreur interne du serveur');
    }
  }
);

/**
 * Mettre à jour le profil utilisateur
 */
export const updateUserProfile = onCall<UpdateUserProfileData>(
  {
    region: 'europe-west1',
    maxInstances: 10,
    memory: '256MiB',
    timeoutSeconds: 30
  },
  async (request) => {
    try {
      // Vérification de l'authentification
      if (!request.auth) {
        throw new HttpsError('unauthenticated', 'Authentification requise');
      }

      const callerId = request.auth.uid;
      const callerRole = request.auth.token.role;

      // Validation des données
      const { userId, displayName, preferences } = UpdateUserProfileSchema.parse(request.data);

      // Vérification des permissions
      // Un utilisateur peut modifier son propre profil, les admins peuvent modifier tous les profils
      if (callerId !== userId && callerRole !== 'admin') {
        throw new HttpsError('permission-denied', 'Permissions insuffisantes');
      }

      const auth = getAuth();
      const db = getFirestore();

      // Transaction pour cohérence des données
      const result = await db.runTransaction(async (transaction) => {
        // Vérifier que l'utilisateur existe
        const userRef = db.collection('users').doc(userId);
        const userDoc = await transaction.get(userRef);

        if (!userDoc.exists) {
          throw new HttpsError('not-found', 'Utilisateur introuvable');
        }

        const updates: any = {
          updatedAt: new Date(),
          updatedBy: callerId
        };

        // Mise à jour du displayName dans Auth et Firestore
        if (displayName !== undefined) {
          await auth.updateUser(userId, { displayName });
          updates.displayName = displayName;
        }

        // Mise à jour des préférences
        if (preferences) {
          const currentPrefs = userDoc.data()?.preferences || {};
          updates.preferences = {
            ...currentPrefs,
            ...preferences,
            // Merge des objets imbriqués
            notifications: {
              ...currentPrefs.notifications,
              ...preferences.notifications
            },
            dashboard: {
              ...currentPrefs.dashboard,
              ...preferences.dashboard
            }
          };
        }

        // Mise à jour des statistiques d'activité
        updates['stats.lastActivityAt'] = new Date();

        transaction.update(userRef, updates);

        return {
          uid: userId,
          displayName: displayName || userDoc.data()?.displayName,
          preferences: updates.preferences || userDoc.data()?.preferences
        };
      });

      logger.info('✅ Profil utilisateur mis à jour', {
        userId,
        updatedBy: callerId,
        fields: Object.keys(request.data).filter(k => k !== 'userId')
      });

      return {
        success: true,
        message: 'Profil mis à jour avec succès',
        user: result
      };

    } catch (error) {
      if (error instanceof HttpsError) throw error;
      
      logger.error('❌ Erreur dans updateUserProfile', {
        error: error instanceof Error ? error.message : String(error),
        callerId: request.auth?.uid
      });
      
      throw new HttpsError('internal', 'Erreur interne du serveur');
    }
  }
);

/**
 * Fonction utilitaire pour valider les permissions
 */
export function hasPermission(userRole: string, action: string, targetUserId?: string, callerId?: string): boolean {
  switch (action) {
    case 'view_user':
      return userRole === 'admin' || userRole === 'regisseur' || (targetUserId === callerId);
    case 'edit_user':
      return userRole === 'admin' || (targetUserId === callerId);
    case 'list_users':
      return userRole === 'admin' || userRole === 'regisseur';
    case 'assign_role':
      return userRole === 'admin';
    default:
      return false;
  }
}
