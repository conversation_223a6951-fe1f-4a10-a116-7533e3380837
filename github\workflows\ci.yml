# Nom du workflow qui apparaîtra dans l'onglet "Actions" de GitHub
name: SIGMA CI - Build, Lint & Test

# Déclencheur : ce workflow se lancera à chaque Pull Request vers la branche `main`
on:
  pull_request:
    branches: [ main ]
  # Permet aussi de le lancer manuellement depuis l'interface GitHub
  workflow_dispatch:

# Définition des tâches (jobs) à exécuter
jobs:
  build-and-test:
    # L'environnement sur lequel le job va tourner (une machine virtuelle Ubuntu)
    runs-on: ubuntu-latest

    # Les étapes du job
    steps:
      # Étape 1 : Récupérer le code de votre dépôt
      - name: Checkout repository
        uses: actions/checkout@v4

      # Étape 2 : Configurer Node.js (version 18 comme dans votre architecture)
      - name: Setup Node.js v18
        uses: actions/setup-node@v4
        with:
          node-version: 18
          cache: 'npm'
          cache-dependency-path: 'src/firebase/functions/package-lock.json'

      # Étape 3 : Installer les dépendances du projet
      - name: Install Dependencies
        run: cd src/firebase/functions && npm install

      # Étape 4 : Linter le code pour vérifier le style (bonne pratique)
      - name: Run Linter
        run: cd src/firebase/functions && npm run lint # Assurez-vous d'avoir un script "lint" dans votre package.json

      # Étape 5 : Lancer les tests unitaires (étape la plus critique)
      - name: Run Unit Tests
        run: cd src/firebase/functions && npm run test

      # Étape 6 : Compiler le code TypeScript pour vérifier qu'il n'y a pas d'erreurs
      - name: Build TypeScript
        run: cd src/firebase/functions && npm run build