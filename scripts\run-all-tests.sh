#!/bin/bash

# Script pour exécuter tous les tests de sécurité SIGMA
# Utilise Firebase Emulator Suite pour les tests isolés

set -e  # Arrêter en cas d'erreur

echo "🧪 Démarrage de la suite complète de tests SIGMA"
echo "================================================"

# Couleurs pour l'affichage
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Fonction pour afficher les messages
log_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

log_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

log_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

log_error() {
    echo -e "${RED}❌ $1${NC}"
}

# Vérifier que Firebase CLI est installé
if ! command -v firebase &> /dev/null; then
    log_error "Firebase CLI n'est pas installé. Installez-le avec: npm install -g firebase-tools"
    exit 1
fi

# Vérifier que Node.js est installé
if ! command -v node &> /dev/null; then
    log_error "Node.js n'est pas installé"
    exit 1
fi

# Vérifier que npm est installé
if ! command -v npm &> /dev/null; then
    log_error "npm n'est pas installé"
    exit 1
fi

log_info "Vérification de l'environnement..."

# Vérifier que nous sommes dans le bon répertoire
if [ ! -f "firebase.json" ]; then
    log_error "Ce script doit être exécuté depuis la racine du projet SIGMA"
    exit 1
fi

# Variables de configuration
EMULATOR_TIMEOUT=30
TEST_TIMEOUT=300
COVERAGE_THRESHOLD=80

# Fonction pour attendre que les émulateurs soient prêts
wait_for_emulators() {
    log_info "Attente du démarrage des émulateurs..."
    
    local timeout=$EMULATOR_TIMEOUT
    local count=0
    
    while [ $count -lt $timeout ]; do
        if curl -s http://localhost:4400 > /dev/null 2>&1; then
            log_success "Émulateurs prêts"
            return 0
        fi
        
        sleep 1
        count=$((count + 1))
        echo -n "."
    done
    
    log_error "Timeout: Les émulateurs ne sont pas prêts après ${timeout}s"
    return 1
}

# Fonction pour nettoyer les processus
cleanup() {
    log_info "Nettoyage des processus..."
    
    # Arrêter les émulateurs
    pkill -f "firebase.*emulators" || true
    pkill -f "java.*firestore" || true
    pkill -f "node.*functions" || true
    
    # Attendre un peu pour que les processus se terminent
    sleep 2
    
    log_success "Nettoyage terminé"
}

# Configurer le nettoyage en cas d'interruption
trap cleanup EXIT INT TERM

# Étape 1: Installation des dépendances
log_info "Installation des dépendances..."

# Dépendances racine
if [ -f "package.json" ]; then
    npm install
    log_success "Dépendances racine installées"
fi

# Dépendances des Cloud Functions
if [ -d "functions" ]; then
    cd functions
    npm install
    cd ..
    log_success "Dépendances Cloud Functions installées"
fi

# Étape 2: Build des Cloud Functions
log_info "Build des Cloud Functions..."
cd functions
npm run build
cd ..
log_success "Cloud Functions buildées"

# Étape 3: Démarrage des émulateurs
log_info "Démarrage des émulateurs Firebase..."

# Démarrer les émulateurs en arrière-plan
firebase emulators:start --only auth,firestore,functions,storage &
EMULATOR_PID=$!

# Attendre que les émulateurs soient prêts
if ! wait_for_emulators; then
    log_error "Impossible de démarrer les émulateurs"
    exit 1
fi

# Étape 4: Exécution des tests unitaires Cloud Functions
log_info "Exécution des tests unitaires Cloud Functions..."

cd functions
if npm test; then
    log_success "Tests unitaires Cloud Functions réussis"
else
    log_error "Échec des tests unitaires Cloud Functions"
    exit 1
fi
cd ..

# Étape 5: Exécution des tests de sécurité Firestore
log_info "Exécution des tests de sécurité Firestore..."

if [ -f "src/tests/security/firestore-rules.test.js" ]; then
    # Installer les dépendances de test si nécessaire
    if [ ! -d "node_modules" ]; then
        npm install
    fi
    
    # Exécuter les tests Firestore
    if npx jest src/tests/security/firestore-rules.test.js --testTimeout=$((TEST_TIMEOUT * 1000)); then
        log_success "Tests de sécurité Firestore réussis"
    else
        log_error "Échec des tests de sécurité Firestore"
        exit 1
    fi
else
    log_warning "Tests de sécurité Firestore non trouvés"
fi

# Étape 6: Exécution des tests de sécurité Storage
log_info "Exécution des tests de sécurité Storage..."

if [ -f "src/tests/security/storage-rules.test.js" ]; then
    if npx jest src/tests/security/storage-rules.test.js --testTimeout=$((TEST_TIMEOUT * 1000)); then
        log_success "Tests de sécurité Storage réussis"
    else
        log_error "Échec des tests de sécurité Storage"
        exit 1
    fi
else
    log_warning "Tests de sécurité Storage non trouvés"
fi

# Étape 7: Exécution des tests d'intégration
log_info "Exécution des tests d'intégration end-to-end..."

if [ -f "src/tests/integration/auth-flow.test.js" ]; then
    if npx jest src/tests/integration/auth-flow.test.js --testTimeout=$((TEST_TIMEOUT * 1000)); then
        log_success "Tests d'intégration réussis"
    else
        log_error "Échec des tests d'intégration"
        exit 1
    fi
else
    log_warning "Tests d'intégration non trouvés"
fi

# Étape 8: Génération du rapport de couverture
log_info "Génération du rapport de couverture..."

cd functions
if npm run test:coverage; then
    log_success "Rapport de couverture généré"
    
    # Vérifier le seuil de couverture
    COVERAGE_LINES=$(grep -o '"lines":{"total":[0-9]*,"covered":[0-9]*' coverage/coverage-summary.json | grep -o '"covered":[0-9]*' | cut -d':' -f2)
    COVERAGE_TOTAL=$(grep -o '"lines":{"total":[0-9]*,"covered":[0-9]*' coverage/coverage-summary.json | grep -o '"total":[0-9]*' | cut -d':' -f2)
    
    if [ -n "$COVERAGE_LINES" ] && [ -n "$COVERAGE_TOTAL" ] && [ "$COVERAGE_TOTAL" -gt 0 ]; then
        COVERAGE_PERCENT=$((COVERAGE_LINES * 100 / COVERAGE_TOTAL))
        
        if [ "$COVERAGE_PERCENT" -ge "$COVERAGE_THRESHOLD" ]; then
            log_success "Couverture de code: ${COVERAGE_PERCENT}% (seuil: ${COVERAGE_THRESHOLD}%)"
        else
            log_warning "Couverture de code: ${COVERAGE_PERCENT}% (en dessous du seuil: ${COVERAGE_THRESHOLD}%)"
        fi
    else
        log_warning "Impossible de calculer la couverture de code"
    fi
else
    log_warning "Impossible de générer le rapport de couverture"
fi
cd ..

# Étape 9: Validation des règles de sécurité
log_info "Validation des règles de sécurité..."

# Vérifier que les règles Firestore sont valides
if firebase firestore:rules:validate; then
    log_success "Règles Firestore valides"
else
    log_error "Règles Firestore invalides"
    exit 1
fi

# Vérifier que les règles Storage sont valides
if firebase storage:rules:validate; then
    log_success "Règles Storage valides"
else
    log_error "Règles Storage invalides"
    exit 1
fi

# Étape 10: Résumé final
echo ""
echo "🎉 TOUS LES TESTS SONT RÉUSSIS !"
echo "================================"
log_success "Tests unitaires Cloud Functions: ✅"
log_success "Tests de sécurité Firestore: ✅"
log_success "Tests de sécurité Storage: ✅"
log_success "Tests d'intégration E2E: ✅"
log_success "Validation des règles: ✅"

echo ""
log_info "Rapports disponibles:"
echo "  - Couverture Cloud Functions: functions/coverage/lcov-report/index.html"
echo "  - Logs des émulateurs: ~/.cache/firebase/emulators/"

echo ""
log_success "Epic E-1 'Sécurité & Authentification' validé avec succès ! 🚀"

exit 0
