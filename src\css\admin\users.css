/* Styles pour l'interface d'administration des utilisateurs */

/* Variables CSS */
:root {
    --primary-color: #667eea;
    --secondary-color: #764ba2;
    --success-color: #10b981;
    --warning-color: #f59e0b;
    --danger-color: #ef4444;
    --info-color: #3b82f6;
    --light-gray: #f8f9fa;
    --medium-gray: #e9ecef;
    --dark-gray: #6c757d;
    --text-color: #1a1a1a;
    --border-radius: 8px;
    --shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    --transition: all 0.3s ease;
}

/* Reset et base */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    background: var(--light-gray);
    color: var(--text-color);
    line-height: 1.6;
}

/* Navigation */
.navbar {
    background: white;
    box-shadow: var(--shadow);
    position: sticky;
    top: 0;
    z-index: 100;
}

.nav-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 64px;
}

.nav-brand {
    display: flex;
    align-items: center;
    gap: 8px;
    font-weight: 600;
    font-size: 18px;
    color: var(--primary-color);
}

.nav-brand .material-icons {
    font-size: 24px;
}

.nav-menu {
    display: flex;
    align-items: center;
    gap: 20px;
}

.nav-link {
    display: flex;
    align-items: center;
    gap: 6px;
    padding: 8px 16px;
    border-radius: var(--border-radius);
    text-decoration: none;
    color: var(--dark-gray);
    transition: var(--transition);
}

.nav-link:hover,
.nav-link.active {
    background: var(--light-gray);
    color: var(--primary-color);
}

.nav-user {
    display: flex;
    align-items: center;
    gap: 12px;
    padding-left: 20px;
    border-left: 1px solid var(--medium-gray);
}

.logout-btn {
    background: none;
    border: none;
    padding: 8px;
    border-radius: var(--border-radius);
    cursor: pointer;
    color: var(--dark-gray);
    transition: var(--transition);
}

.logout-btn:hover {
    background: var(--light-gray);
    color: var(--danger-color);
}

/* Contenu principal */
.main-content {
    padding: 20px;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
}

/* En-tête de page */
.page-header {
    margin-bottom: 30px;
}

.page-header h1 {
    display: flex;
    align-items: center;
    gap: 12px;
    font-size: 28px;
    font-weight: 700;
    margin-bottom: 8px;
}

.page-description {
    color: var(--dark-gray);
    font-size: 16px;
}

/* Section des filtres */
.filters-section {
    background: white;
    padding: 20px;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow);
    margin-bottom: 20px;
    display: flex;
    gap: 20px;
    align-items: center;
    flex-wrap: wrap;
}

.search-box {
    position: relative;
    flex: 1;
    min-width: 300px;
}

.search-box .material-icons {
    position: absolute;
    left: 12px;
    top: 50%;
    transform: translateY(-50%);
    color: var(--dark-gray);
    font-size: 20px;
}

.search-box input {
    width: 100%;
    padding: 12px 12px 12px 44px;
    border: 1px solid var(--medium-gray);
    border-radius: var(--border-radius);
    font-size: 14px;
    transition: var(--transition);
}

.search-box input:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.filter-controls {
    display: flex;
    gap: 12px;
    align-items: center;
}

.filter-controls select {
    padding: 10px 12px;
    border: 1px solid var(--medium-gray);
    border-radius: var(--border-radius);
    background: white;
    font-size: 14px;
}

/* Boutons */
.btn {
    display: inline-flex;
    align-items: center;
    gap: 6px;
    padding: 10px 16px;
    border: none;
    border-radius: var(--border-radius);
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: var(--transition);
    text-decoration: none;
}

.btn-primary {
    background: var(--primary-color);
    color: white;
}

.btn-primary:hover {
    background: #5a6fd8;
}

.btn-secondary {
    background: var(--medium-gray);
    color: var(--text-color);
}

.btn-secondary:hover {
    background: #dee2e6;
}

.btn-danger {
    background: var(--danger-color);
    color: white;
}

.btn-danger:hover {
    background: #dc2626;
}

.btn-sm {
    padding: 6px 12px;
    font-size: 12px;
}

/* États de chargement */
.loading-state {
    text-align: center;
    padding: 60px 20px;
    background: white;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow);
}

.spinner {
    width: 40px;
    height: 40px;
    border: 4px solid var(--light-gray);
    border-top: 4px solid var(--primary-color);
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto 16px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Section des utilisateurs */
.users-section {
    background: white;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow);
    overflow: hidden;
}

.users-header {
    padding: 20px;
    border-bottom: 1px solid var(--medium-gray);
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.users-header h2 {
    font-size: 20px;
    font-weight: 600;
}

.count-badge {
    background: var(--light-gray);
    color: var(--dark-gray);
    padding: 4px 12px;
    border-radius: 20px;
    font-size: 12px;
    font-weight: 500;
}

/* Table des utilisateurs */
.users-table-container {
    overflow-x: auto;
}

.users-table {
    width: 100%;
    border-collapse: collapse;
}

.users-table th,
.users-table td {
    padding: 16px;
    text-align: left;
    border-bottom: 1px solid var(--medium-gray);
}

.users-table th {
    background: var(--light-gray);
    font-weight: 600;
    font-size: 14px;
    color: var(--dark-gray);
}

.users-table td {
    font-size: 14px;
}

.user-info {
    display: flex;
    align-items: center;
    gap: 12px;
}

.user-avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: var(--light-gray);
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--dark-gray);
}

.user-details h4 {
    font-size: 14px;
    font-weight: 500;
    margin-bottom: 2px;
}

.user-details p {
    font-size: 12px;
    color: var(--dark-gray);
}

.role-badge {
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
    font-weight: 500;
}

.role-admin {
    background: #fef2f2;
    color: #dc2626;
}

.role-regisseur {
    background: #fef3c7;
    color: #d97706;
}

.role-utilisateur {
    background: #f0f9ff;
    color: #0369a1;
}

.status-badge {
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
    font-weight: 500;
}

.status-active {
    background: #f0fdf4;
    color: #16a34a;
}

.status-inactive {
    background: #fef2f2;
    color: #dc2626;
}

.actions-cell {
    display: flex;
    gap: 8px;
}

/* Pagination */
.pagination {
    padding: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 16px;
    border-top: 1px solid var(--medium-gray);
}

/* Messages */
.message {
    padding: 12px 16px;
    border-radius: var(--border-radius);
    margin-bottom: 16px;
    display: flex;
    align-items: center;
    gap: 8px;
}

.message-success {
    background: #f0fdf4;
    color: #16a34a;
    border: 1px solid #bbf7d0;
}

.message-error {
    background: #fef2f2;
    color: #dc2626;
    border: 1px solid #fecaca;
}

.message-info {
    background: #f0f9ff;
    color: #0369a1;
    border: 1px solid #bae6fd;
}

/* Modals */
.modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
}

.modal-content {
    background: white;
    border-radius: var(--border-radius);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.2);
    max-width: 500px;
    width: 90%;
    max-height: 90vh;
    overflow-y: auto;
}

.modal-header {
    padding: 20px;
    border-bottom: 1px solid var(--medium-gray);
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.modal-header h3 {
    font-size: 18px;
    font-weight: 600;
}

.modal-close {
    background: none;
    border: none;
    padding: 4px;
    border-radius: 4px;
    cursor: pointer;
    color: var(--dark-gray);
}

.modal-close:hover {
    background: var(--light-gray);
}

.modal-body {
    padding: 20px;
}

.modal-footer {
    padding: 20px;
    border-top: 1px solid var(--medium-gray);
    display: flex;
    gap: 12px;
    justify-content: flex-end;
}

.form-group {
    margin-bottom: 16px;
}

.form-group label {
    display: block;
    margin-bottom: 6px;
    font-weight: 500;
    font-size: 14px;
}

.form-control {
    width: 100%;
    padding: 10px 12px;
    border: 1px solid var(--medium-gray);
    border-radius: var(--border-radius);
    font-size: 14px;
    transition: var(--transition);
}

.form-control:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

/* Responsive */
@media (max-width: 768px) {
    .nav-container {
        padding: 0 15px;
    }

    .nav-menu {
        gap: 10px;
    }

    .nav-link span {
        display: none;
    }

    .filters-section {
        flex-direction: column;
        align-items: stretch;
    }

    .search-box {
        min-width: auto;
    }

    .filter-controls {
        justify-content: space-between;
    }

    .users-table th,
    .users-table td {
        padding: 12px 8px;
        font-size: 12px;
    }

    .user-info {
        flex-direction: column;
        align-items: flex-start;
        gap: 8px;
    }

    .user-avatar {
        width: 32px;
        height: 32px;
    }

    .actions-cell {
        flex-direction: column;
        gap: 4px;
    }

    .modal-content {
        margin: 20px;
        width: calc(100% - 40px);
    }

    .modal-footer {
        flex-direction: column-reverse;
    }

    .btn {
        justify-content: center;
    }
}
