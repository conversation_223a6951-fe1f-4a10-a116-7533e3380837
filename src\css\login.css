/* Reset et base */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 20px;
}

/* Container principal */
.login-container {
    width: 100%;
    max-width: 400px;
    margin: 0 auto;
}

.login-card {
    background: white;
    border-radius: 16px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
    overflow: hidden;
    animation: slideUp 0.6s ease-out;
}

@keyframes slideUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Header */
.login-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    text-align: center;
    padding: 40px 30px;
}

.logo {
    margin-bottom: 16px;
}

.logo .material-icons {
    font-size: 48px;
    opacity: 0.9;
}

.login-header h1 {
    font-size: 32px;
    font-weight: 700;
    margin-bottom: 8px;
    letter-spacing: -0.5px;
}

.subtitle {
    font-size: 14px;
    opacity: 0.8;
    font-weight: 400;
}

/* Contenu */
.login-content {
    padding: 40px 30px;
}

.login-form h2 {
    font-size: 24px;
    font-weight: 600;
    color: #1a1a1a;
    margin-bottom: 8px;
    text-align: center;
}

.login-description {
    color: #666;
    text-align: center;
    margin-bottom: 32px;
    line-height: 1.5;
}

/* Bouton Google */
.google-signin-btn {
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 12px;
    padding: 16px 24px;
    border: 2px solid #e0e0e0;
    border-radius: 12px;
    background: white;
    color: #333;
    font-size: 16px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    text-decoration: none;
}

.google-signin-btn:hover {
    border-color: #4285F4;
    box-shadow: 0 4px 12px rgba(66, 133, 244, 0.15);
    transform: translateY(-1px);
}

.google-signin-btn:active {
    transform: translateY(0);
}

.google-icon {
    width: 20px;
    height: 20px;
}

/* États de chargement */
.loading-state {
    text-align: center;
    padding: 40px 0;
}

.spinner {
    width: 40px;
    height: 40px;
    border: 4px solid #f3f3f3;
    border-top: 4px solid #667eea;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto 16px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.loading-state p {
    color: #666;
    font-weight: 500;
}

/* Messages */
.error-message,
.success-message {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 12px 16px;
    border-radius: 8px;
    margin-top: 20px;
    font-size: 14px;
    font-weight: 500;
}

.error-message {
    background: #fef2f2;
    color: #dc2626;
    border: 1px solid #fecaca;
}

.success-message {
    background: #f0fdf4;
    color: #16a34a;
    border: 1px solid #bbf7d0;
}

.error-message .material-icons,
.success-message .material-icons {
    font-size: 18px;
}

/* Footer */
.login-footer {
    background: #f8f9fa;
    padding: 20px 30px;
    text-align: center;
    border-top: 1px solid #e9ecef;
}

.login-footer p {
    color: #666;
    font-size: 12px;
}

/* Responsive */
@media (max-width: 480px) {
    .login-container {
        padding: 10px;
    }
    
    .login-header {
        padding: 30px 20px;
    }
    
    .login-content {
        padding: 30px 20px;
    }
    
    .login-footer {
        padding: 15px 20px;
    }
    
    .login-header h1 {
        font-size: 28px;
    }
    
    .google-signin-btn {
        padding: 14px 20px;
        font-size: 15px;
    }
}
