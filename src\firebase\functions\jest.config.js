/**
 * Configuration Jest pour les tests des Cloud Functions SIGMA
 */

module.exports = {
  // Environnement de test
  testEnvironment: 'node',
  
  // Extensions de fichiers à traiter
  moduleFileExtensions: ['ts', 'tsx', 'js', 'jsx', 'json'],
  
  // Transformation des fichiers TypeScript
  transform: {
    '^.+\\.(ts|tsx)$': 'ts-jest'
  },
  
  // Patterns de fichiers de test
  testMatch: [
    '**/src/tests/**/*.test.(ts|js)',
    '**/src/**/__tests__/**/*.(ts|js)',
    '**/src/**/*.(test|spec).(ts|js)'
  ],
  
  // Répertoires à ignorer
  testPathIgnorePatterns: [
    '/node_modules/',
    '/lib/',
    '/dist/'
  ],
  
  // Configuration de couverture de code
  collectCoverage: true,
  collectCoverageFrom: [
    'src/**/*.{ts,js}',
    '!src/**/*.d.ts',
    '!src/tests/**/*',
    '!src/**/__tests__/**/*'
  ],
  coverageDirectory: 'coverage',
  coverageReporters: ['text', 'lcov', 'html'],
  coverageThreshold: {
    global: {
      branches: 80,
      functions: 80,
      lines: 80,
      statements: 80
    }
  },
  
  // Configuration des modules
  moduleNameMapping: {
    '^@/(.*)$': '<rootDir>/src/$1'
  },
  
  // Setup et teardown
  setupFilesAfterEnv: ['<rootDir>/src/tests/setup.ts'],
  
  // Timeout pour les tests (important pour les tests d'intégration)
  testTimeout: 30000,
  
  // Variables d'environnement pour les tests
  setupFiles: ['<rootDir>/src/tests/env.ts'],
  
  // Configuration spécifique pour Firebase
  globals: {
    'ts-jest': {
      tsconfig: 'tsconfig.json'
    }
  },
  
  // Verbose pour plus de détails
  verbose: true,
  
  // Forcer la sortie en cas d'erreur
  forceExit: true,
  
  // Nettoyer les mocks après chaque test
  clearMocks: true,
  
  // Restaurer les mocks après chaque test
  restoreMocks: true
};
