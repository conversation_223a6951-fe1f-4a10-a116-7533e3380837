{"name": "sigma-functions", "version": "1.0.0", "main": "lib/index.js", "scripts": {"build": "tsc", "serve": "firebase emulators:start --only functions", "shell": "npm run build && firebase functions:shell", "start": "npm run shell", "deploy": "firebase deploy --only functions", "logs": "firebase functions:log", "test": "jest"}, "dependencies": {"firebase-admin": "^12.1.0", "firebase-functions": "^5.0.1", "pdf-lib": "^1.17.1", "zod": "^3.23.8"}, "devDependencies": {"typescript": "^5.4.5", "jest": "^29.7.0", "ts-jest": "^29.1.2", "cypress": "^13.13.0", "leaflet": "^1.9.4"}}