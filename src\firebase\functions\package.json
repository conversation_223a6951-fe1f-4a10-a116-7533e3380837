{"name": "functions", "scripts": {"lint": "eslint --ext .js,.ts .", "build": "tsc", "build:watch": "tsc --watch", "serve": "npm run build && firebase emulators:start --only functions", "shell": "npm run build && firebase functions:shell", "start": "npm run shell", "deploy": "firebase deploy --only functions", "logs": "firebase functions:log", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "test:emulators": "firebase emulators:exec --only auth,firestore,functions,storage 'npm test'", "test:ci": "npm run build && npm run test:emulators"}, "engines": {"node": "22"}, "main": "lib/index.js", "dependencies": {"firebase-admin": "^12.6.0", "firebase-functions": "^6.0.1", "zod": "^4.0.14"}, "devDependencies": {"@firebase/rules-unit-testing": "^3.0.4", "@jest/globals": "^29.7.0", "@types/jest": "^29.5.12", "@typescript-eslint/eslint-plugin": "^5.12.0", "@typescript-eslint/parser": "^5.12.0", "eslint": "^8.9.0", "eslint-config-google": "^0.14.0", "eslint-plugin-import": "^2.25.4", "firebase": "^10.13.2", "firebase-functions-test": "^3.1.0", "jest": "^29.7.0", "ts-jest": "^29.2.5", "typescript": "^5.7.3"}, "private": true}