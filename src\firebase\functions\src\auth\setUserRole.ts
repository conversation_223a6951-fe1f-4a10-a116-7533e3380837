/**
 * Cloud Function pour assigner les rôles utilisateurs via Custom Claims
 * Respecte les guidelines SIGMA : validation des permissions, logging, transactions
 */

import { onCall, HttpsError } from 'firebase-functions/v2/https';
import { getAuth } from 'firebase-admin/auth';
import { getFirestore } from 'firebase-admin/firestore';
import * as logger from 'firebase-functions/logger';
import { z } from 'zod';
import { requireRole, logSuccessfulAccess, validateAndSanitizeInput } from '../utils/auth';

// Schéma de validation pour les données d'entrée
const SetUserRoleSchema = z.object({
  userId: z.string().min(1, 'ID utilisateur requis'),
  role: z.enum(['admin', 'regisseur', 'utilisateur'], {
    message: 'Rôle invalide. Valeurs autorisées: admin, regisseur, utilisateur'
  }),
  reason: z.string().optional() // Raison du changement de rôle (pour audit)
});

type SetUserRoleData = z.infer<typeof SetUserRoleSchema>;

/**
 * Cloud Function callable pour assigner un rôle à un utilisateur
 * 
 * Sécurité:
 * - Seuls les admins peuvent assigner des rôles
 * - Validation stricte des données d'entrée
 * - Logging complet pour audit
 * - Transaction Firestore pour cohérence
 * 
 * @param data - { userId: string, role: 'admin'|'regisseur'|'utilisateur', reason?: string }
 * @param context - Contexte d'authentification Firebase
 */
export const setUserRole = onCall<SetUserRoleData>(
  {
    region: 'europe-west1', // Région européenne pour conformité RGPD
    maxInstances: 5, // Limitation pour contrôle des coûts
    memory: '256MiB', // Mémoire optimisée
    timeoutSeconds: 30
  },
  async (request) => {
    const startTime = Date.now();

    try {
      // 1. Vérification de l'authentification et des permissions (admin uniquement)
      const caller = requireRole(request, 'admin');
      const callerId = caller.uid;
      const callerEmail = caller.email;

      logger.info('🔐 Demande de changement de rôle', {
        callerId,
        callerEmail,
        callerRole: caller.role,
        requestData: request.data
      });

      // 2. Validation des données d'entrée
      const validatedData = validateAndSanitizeInput(
        request.data,
        (data) => SetUserRoleSchema.parse(data),
        request
      );

      const { userId, role, reason } = validatedData;

      // 4. Vérification que l'utilisateur cible existe
      const auth = getAuth();
      let targetUser;
      try {
        targetUser = await auth.getUser(userId);
      } catch (error) {
        logger.warn('❌ Utilisateur cible introuvable', { userId, callerId: caller.uid });
        throw new HttpsError('not-found', 'Utilisateur introuvable');
      }

      // 5. Récupération du rôle actuel
      const currentClaims = targetUser.customClaims || {};
      const currentRole = currentClaims.role || 'utilisateur';

      // 6. Vérification si le changement est nécessaire
      if (currentRole === role) {
        logger.info('ℹ️ Rôle déjà assigné', { userId, role, callerId });
        return {
          success: true,
          message: `L'utilisateur a déjà le rôle ${role}`,
          previousRole: currentRole,
          newRole: role
        };
      }

      // 7. Transaction Firestore pour cohérence des données
      const db = getFirestore();
      const result = await db.runTransaction(async (transaction) => {
        // Mettre à jour les Custom Claims
        await auth.setCustomUserClaims(userId, {
          ...currentClaims,
          role: role,
          roleAssignedAt: new Date().toISOString(),
          roleAssignedBy: callerId
        });

        // Mettre à jour le document utilisateur dans Firestore
        const userRef = db.collection('users').doc(userId);
        const userDoc = await transaction.get(userRef);

        const userData = {
          uid: userId,
          email: targetUser.email,
          displayName: targetUser.displayName || '',
          role: role,
          roleHistory: [
            ...(userDoc.exists ? userDoc.data()?.roleHistory || [] : []),
            {
              previousRole: currentRole,
              newRole: role,
              changedAt: new Date(),
              changedBy: callerId,
              changedByEmail: callerEmail,
              reason: reason || 'Non spécifiée'
            }
          ],
          updatedAt: new Date(),
          updatedBy: callerId
        };

        if (userDoc.exists) {
          transaction.update(userRef, userData);
        } else {
          transaction.set(userRef, {
            ...userData,
            createdAt: new Date(),
            createdBy: callerId
          });
        }

        return {
          previousRole: currentRole,
          newRole: role,
          userEmail: targetUser.email
        };
      });

      // 8. Logging de succès
      const executionTime = Date.now() - startTime;
      logger.info('✅ Rôle assigné avec succès', {
        userId,
        userEmail: result.userEmail,
        previousRole: result.previousRole,
        newRole: result.newRole,
        assignedBy: callerId,
        assignedByEmail: callerEmail,
        reason: reason || 'Non spécifiée',
        executionTimeMs: executionTime
      });

      // Log du succès
      logSuccessfulAccess(caller, 'setUserRole', `user:${userId}`, {
        targetUserId: userId,
        targetUserEmail: result.userEmail,
        previousRole: result.previousRole,
        newRole: result.newRole,
        reason: reason || 'Non spécifiée',
        executionTimeMs: executionTime
      });

      return {
        success: true,
        message: `Rôle ${role} assigné avec succès à ${result.userEmail}`,
        previousRole: result.previousRole,
        newRole: result.newRole,
        executionTimeMs: executionTime
      };

    } catch (error) {
      const executionTime = Date.now() - startTime;
      
      // Si c'est déjà une HttpsError, la relancer
      if (error instanceof HttpsError) {
        logger.error('❌ Erreur contrôlée dans setUserRole', {
          code: error.code,
          message: error.message,
          callerId: request.auth?.uid,
          executionTimeMs: executionTime
        });
        throw error;
      }

      // Erreur inattendue
      logger.error('❌ Erreur inattendue dans setUserRole', {
        error: error instanceof Error ? error.message : String(error),
        stack: error instanceof Error ? error.stack : undefined,
        callerId: request.auth?.uid,
        executionTimeMs: executionTime
      });

      throw new HttpsError('internal', 'Erreur interne du serveur');
    }
  }
);

/**
 * Fonction utilitaire pour valider un rôle
 */
export function isValidRole(role: string): role is 'admin' | 'regisseur' | 'utilisateur' {
  return ['admin', 'regisseur', 'utilisateur'].includes(role);
}

/**
 * Fonction utilitaire pour vérifier si un utilisateur a les permissions pour assigner un rôle
 */
export function canAssignRole(userRole: string, targetRole: string): boolean {
  // Seuls les admins peuvent assigner des rôles
  if (userRole !== 'admin') {
    return false;
  }
  
  // Les admins peuvent assigner tous les rôles
  return isValidRole(targetRole);
}
