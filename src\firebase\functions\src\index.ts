/**
 * SIGMA - Cloud Functions Index
 * Point d'entrée principal pour toutes les Cloud Functions
 */

import { setGlobalOptions } from "firebase-functions";
import { initializeApp } from "firebase-admin/app";
import * as logger from "firebase-functions/logger";

// Initialiser Firebase Admin SDK
initializeApp();

// Configuration globale pour le contrôle des coûts et performances
// Région européenne pour conformité RGPD
setGlobalOptions({
  maxInstances: 10,
  region: 'europe-west1'
});

// ===== FONCTIONS D'AUTHENTIFICATION =====

// Gestion des rôles utilisateurs
export { setUserRole } from './auth/setUserRole';

// Trigger de création d'utilisateur
export { onUserCreate } from './auth/onUserCreate';

// Gestion des utilisateurs
export {
  getUserRole,
  listUsers,
  updateUserProfile
} from './auth/userManagement';

// ===== FONCTIONS MÉTIER (À IMPLÉMENTER) =====

// TODO: Fonctions pour la gestion des emprunts
// export { createEmprunt, updateEmprunt, deleteEmprunt } from './emprunts';

// TODO: Fonctions pour la gestion des stocks
// export { updateStock, getStockStatus } from './stocks';

// TODO: Fonctions pour la gestion des modules
// export { createModule, updateModule, deleteModule } from './modules';

// TODO: Fonctions pour la gestion des livraisons
// export { createLivraison, updateLivraison } from './livraisons';

// ===== FONCTIONS UTILITAIRES =====

// Utilitaires d'authentification et validation
export * from './utils/auth';
export * from './utils/validation';
export * from './utils/audit';

// TODO: Fonctions de notification
// export { sendNotification, sendEmail } from './notifications';

// TODO: Fonctions de reporting
// export { generateReport, exportData } from './reports';

logger.info('🚀 SIGMA Cloud Functions initialisées', {
  region: 'europe-west1',
  maxInstances: 10,
  timestamp: new Date().toISOString()
});
