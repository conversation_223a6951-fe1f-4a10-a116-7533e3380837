/**
 * Tests unitaires pour les Cloud Functions d'authentification SIGMA
 * Utilise Firebase Emulator Suite pour les tests isolés
 */

import { describe, it, beforeAll, afterAll, beforeEach, afterEach, expect } from '@jest/globals';
import { initializeTestEnvironment, RulesTestEnvironment } from '@firebase/rules-unit-testing';
import { getAuth } from 'firebase-admin/auth';
import { getFirestore } from 'firebase-admin/firestore';
import { httpsCallable, getFunctions } from 'firebase/functions';
import { initializeApp } from 'firebase/app';


// Configuration des tests
const PROJECT_ID = 'sigma-test-project';
const EMULATOR_HOST = 'localhost';
const FIRESTORE_PORT = 8080;

describe('Cloud Functions d\'Authentification', () => {
  let testEnv: RulesTestEnvironment;
  let adminAuth: any;
  let adminFirestore: any;
  let testApp: any;

  // Utilisateurs de test
  const testUsers = {
    admin: {
      uid: 'test-admin-uid',
      email: '<EMAIL>',
      displayName: 'Test Admin',
      role: 'admin'
    },
    regisseur: {
      uid: 'test-regisseur-uid',
      email: '<EMAIL>',
      displayName: 'Test Régisseur',
      role: 'regisseur'
    },
    utilisateur: {
      uid: 'test-utilisateur-uid',
      email: '<EMAIL>',
      displayName: 'Test Utilisateur',
      role: 'utilisateur'
    },
    newUser: {
      uid: 'test-new-user-uid',
      email: '<EMAIL>',
      displayName: 'New User'
    }
  };

  beforeAll(async () => {
    // Initialiser l'environnement de test
    testEnv = await initializeTestEnvironment({
      projectId: PROJECT_ID,
      firestore: {
        host: EMULATOR_HOST,
        port: FIRESTORE_PORT,
        rules: `
          rules_version = '2';
          service cloud.firestore {
            match /databases/{database}/documents {
              match /{document=**} {
                allow read, write: if true;
              }
            }
          }
        `
      }
    });

    // Obtenir les services admin
    adminAuth = getAuth();
    adminFirestore = getFirestore();

    // Créer une app Firebase pour les tests
    testApp = initializeApp({
      projectId: PROJECT_ID,
      apiKey: 'fake-api-key',
      authDomain: `${PROJECT_ID}.firebaseapp.com`,
      storageBucket: `${PROJECT_ID}.appspot.com`,
    });
  });

  afterAll(async () => {
    await testEnv.cleanup();
  });

  beforeEach(async () => {
    // Nettoyer les données avant chaque test
    await testEnv.clearFirestore();
    
    // Créer les utilisateurs de test
    for (const user of Object.values(testUsers)) {
      if (user.uid !== testUsers.newUser.uid) {
        await adminAuth.createUser({
          uid: user.uid,
          email: user.email,
          displayName: user.displayName
        });

        // Assigner les rôles via Custom Claims
        if ('role' in user) {
          await adminAuth.setCustomUserClaims(user.uid, { role: user.role });
        }

        // Créer le document utilisateur dans Firestore
        await adminFirestore.collection('users').doc(user.uid).set({
          email: user.email,
          displayName: user.displayName,
          role: 'role' in user ? user.role : 'utilisateur',
          createdAt: new Date(),
          isActive: true,
          preferences: {
            language: 'fr',
            theme: 'light',
            notifications: {
              email: true,
              browser: true,
              emprunts: true,
              stocks: true,
              livraisons: true
            }
          },
          stats: {
            empruntsCount: 0,
            lastActivityAt: new Date()
          }
        });
      }
    }
  });

  afterEach(async () => {
    // Nettoyer après chaque test
    await testEnv.clearFirestore();
    
    // Supprimer les utilisateurs de test
    for (const user of Object.values(testUsers)) {
      try {
        await adminAuth.deleteUser(user.uid);
      } catch (error) {
        // Ignorer si l'utilisateur n'existe pas
      }
    }
  });

  describe('setUserRole Cloud Function', () => {
    it('devrait permettre à un admin d\'assigner un rôle', async () => {
      const functions = getFunctions(testApp);
      const setUserRole = httpsCallable(functions, 'setUserRole');

      // Tester l'assignation de rôle
      const result = await setUserRole({
        userId: testUsers.utilisateur.uid,
        role: 'regisseur',
        reason: 'Promotion pour test'
      });

      const resultData = result.data as any;
      expect(resultData.success).toBe(true);
      expect(resultData.newRole).toBe('regisseur');
      expect(resultData.previousRole).toBe('utilisateur');

      // Vérifier que le Custom Claim a été mis à jour
      const userRecord = await adminAuth.getUser(testUsers.utilisateur.uid);
      expect(userRecord.customClaims?.role).toBe('regisseur');

      // Vérifier que l'historique a été enregistré
      const historySnapshot = await adminFirestore
        .collection('roleHistory')
        .where('userId', '==', testUsers.utilisateur.uid)
        .get();
      
      expect(historySnapshot.size).toBe(1);
      const historyData = historySnapshot.docs[0].data();
      expect(historyData.newRole).toBe('regisseur');
      expect(historyData.previousRole).toBe('utilisateur');
      expect(historyData.reason).toBe('Promotion pour test');
    });

    it('devrait refuser l\'accès à un non-admin', async () => {
      const functions = getFunctions(testApp);
      const setUserRole = httpsCallable(functions, 'setUserRole');

      await expect(setUserRole({
        userId: testUsers.regisseur.uid,
        role: 'admin'
      })).rejects.toThrow();
    });

    it('devrait valider les données d\'entrée', async () => {
      const functions = getFunctions(testApp);
      const setUserRole = httpsCallable(functions, 'setUserRole');

      // Test avec rôle invalide
      await expect(setUserRole({
        userId: testUsers.utilisateur.uid,
        role: 'invalid-role'
      })).rejects.toThrow();

      // Test avec userId manquant
      await expect(setUserRole({
        role: 'regisseur'
      })).rejects.toThrow();

      // Test avec utilisateur inexistant
      await expect(setUserRole({
        userId: 'non-existent-user',
        role: 'regisseur'
      })).rejects.toThrow();
    });

    it('devrait gérer l\'assignation du même rôle', async () => {
      const functions = getFunctions(testApp);
      const setUserRole = httpsCallable(functions, 'setUserRole');

      const result = await setUserRole({
        userId: testUsers.regisseur.uid,
        role: 'regisseur' // Même rôle
      });

      const resultData = result.data as any;
      expect(resultData.success).toBe(true);
      expect(resultData.message).toContain('déjà le rôle');
    });
  });

  describe('onUserCreate Trigger', () => {
    it('devrait créer un document utilisateur avec le rôle par défaut', async () => {
      // Créer un nouvel utilisateur (simule le trigger)
      const newUser = await adminAuth.createUser({
        uid: testUsers.newUser.uid,
        email: testUsers.newUser.email,
        displayName: testUsers.newUser.displayName
      });

      // Simuler le trigger onUserCreate
      // Note: Dans un vrai test, le trigger se déclencherait automatiquement
      // Ici nous testons la logique directement
      
      // Vérifier que les Custom Claims ont été assignés
      await adminAuth.setCustomUserClaims(newUser.uid, { role: 'utilisateur' });
      
      // Créer le document utilisateur
      await adminFirestore.collection('users').doc(newUser.uid).set({
        email: newUser.email,
        displayName: newUser.displayName,
        role: 'utilisateur',
        createdAt: new Date(),
        isActive: true,
        preferences: {
          language: 'fr',
          theme: 'light',
          notifications: {
            email: true,
            browser: true,
            emprunts: true,
            stocks: true,
            livraisons: true
          }
        },
        stats: {
          empruntsCount: 0,
          lastActivityAt: new Date()
        }
      });

      // Vérifier le document créé
      const userDoc = await adminFirestore.collection('users').doc(newUser.uid).get();
      expect(userDoc.exists).toBe(true);
      
      const userData = userDoc.data();
      expect(userData?.role).toBe('utilisateur');
      expect(userData?.email).toBe(testUsers.newUser.email);
      expect(userData?.isActive).toBe(true);
      expect(userData?.preferences).toBeDefined();
      expect(userData?.stats).toBeDefined();

      // Vérifier les Custom Claims
      const userRecord = await adminAuth.getUser(newUser.uid);
      expect(userRecord.customClaims?.role).toBe('utilisateur');
    });
  });

  describe('getUserRole Cloud Function', () => {
    it('devrait retourner les informations d\'un utilisateur pour un admin', async () => {
      const functions = getFunctions(testApp);
      const getUserRole = httpsCallable(functions, 'getUserRole');

      const result = await getUserRole({
        userId: testUsers.regisseur.uid
      });

      const resultData = result.data as any;
      expect(resultData.uid).toBe(testUsers.regisseur.uid);
      expect(resultData.email).toBe(testUsers.regisseur.email);
      expect(resultData.role).toBe('regisseur');
      expect(resultData.displayName).toBe(testUsers.regisseur.displayName);
    });

    it('devrait permettre à un utilisateur de voir ses propres informations', async () => {
      const functions = getFunctions(testApp);
      const getUserRole = httpsCallable(functions, 'getUserRole');

      const result = await getUserRole({
        userId: testUsers.utilisateur.uid
      });

      const resultData = result.data as any;
      expect(resultData.uid).toBe(testUsers.utilisateur.uid);
      expect(resultData.role).toBe('utilisateur');
    });

    it('devrait refuser l\'accès aux informations d\'autres utilisateurs', async () => {
      const functions = getFunctions(testApp);
      const getUserRole = httpsCallable(functions, 'getUserRole');

      await expect(getUserRole({
        userId: testUsers.admin.uid
      })).rejects.toThrow();
    });
  });

  describe('listUsers Cloud Function', () => {
    it('devrait permettre à un admin de lister les utilisateurs', async () => {
      const functions = getFunctions(testApp);
      const listUsers = httpsCallable(functions, 'listUsers');

      const result = await listUsers({
        limit: 10
      });

      const resultData = result.data as any;
      expect(resultData.users).toBeDefined();
      expect(Array.isArray(resultData.users)).toBe(true);
      expect(resultData.users.length).toBeGreaterThan(0);

      // Vérifier qu'on a au moins nos utilisateurs de test
      const userEmails = resultData.users.map((u: any) => u.email);
      expect(userEmails).toContain(testUsers.admin.email);
      expect(userEmails).toContain(testUsers.regisseur.email);
      expect(userEmails).toContain(testUsers.utilisateur.email);
    });

    it('devrait refuser l\'accès aux non-admins', async () => {
      const functions = getFunctions(testApp);
      const listUsers = httpsCallable(functions, 'listUsers');

      await expect(listUsers({
        limit: 10
      })).rejects.toThrow();
    });

    it('devrait supporter la pagination', async () => {
      const functions = getFunctions(testApp);
      const listUsers = httpsCallable(functions, 'listUsers');

      const result = await listUsers({
        limit: 2
      });

      const resultData = result.data as any;
      expect(resultData.users.length).toBeLessThanOrEqual(2);
      expect(resultData.hasMore).toBeDefined();
      expect(resultData.lastUserId).toBeDefined();
    });

    it('devrait supporter le filtrage par rôle', async () => {
      const functions = getFunctions(testApp);
      const listUsers = httpsCallable(functions, 'listUsers');

      const result = await listUsers({
        limit: 10,
        role: 'admin'
      });

      const resultData = result.data as any;
      expect(resultData.users).toBeDefined();
      resultData.users.forEach((user: any) => {
        expect(user.role).toBe('admin');
      });
    });
  });

  describe('updateUserProfile Cloud Function', () => {
    it('devrait permettre à un utilisateur de mettre à jour son profil', async () => {
      const functions = getFunctions(testApp);
      const updateUserProfile = httpsCallable(functions, 'updateUserProfile');

      const result = await updateUserProfile({
        userId: testUsers.utilisateur.uid,
        displayName: 'Nouveau Nom',
        preferences: {
          language: 'en',
          theme: 'dark'
        }
      });

      const resultData = result.data as any;
      expect(resultData.success).toBe(true);

      // Vérifier que le document a été mis à jour
      const userDoc = await adminFirestore.collection('users').doc(testUsers.utilisateur.uid).get();
      const userData = userDoc.data();
      expect(userData?.displayName).toBe('Nouveau Nom');
      expect(userData?.preferences.language).toBe('en');
      expect(userData?.preferences.theme).toBe('dark');
    });

    it('devrait permettre à un admin de mettre à jour n\'importe quel profil', async () => {
      const functions = getFunctions(testApp);
      const updateUserProfile = httpsCallable(functions, 'updateUserProfile');

      const result = await updateUserProfile({
        userId: testUsers.utilisateur.uid,
        displayName: 'Modifié par Admin'
      });

      const resultData = result.data as any;
      expect(resultData.success).toBe(true);

      const userDoc = await adminFirestore.collection('users').doc(testUsers.utilisateur.uid).get();
      const userData = userDoc.data();
      expect(userData?.displayName).toBe('Modifié par Admin');
    });

    it('devrait refuser la modification du profil d\'un autre utilisateur', async () => {
      const functions = getFunctions(testApp);
      const updateUserProfile = httpsCallable(functions, 'updateUserProfile');

      await expect(updateUserProfile({
        userId: testUsers.regisseur.uid,
        displayName: 'Tentative de modification'
      })).rejects.toThrow();
    });
  });
});
