/**
 * Variables d'environnement pour les tests
 */

// Configuration des émulateurs Firebase
process.env.FIRESTORE_EMULATOR_HOST = 'localhost:8080';
process.env.FIREBASE_AUTH_EMULATOR_HOST = 'localhost:9099';
process.env.FIREBASE_STORAGE_EMULATOR_HOST = 'localhost:9199';
process.env.FUNCTIONS_EMULATOR_HOST = 'localhost:5001';

// Configuration du projet de test
process.env.GCLOUD_PROJECT = 'sigma-test-project';
process.env.FIREBASE_PROJECT_ID = 'sigma-test-project';

// Désactiver les warnings des émulateurs
process.env.FIREBASE_EMULATOR_HUB = 'localhost:4400';

// Configuration pour les tests
process.env.NODE_ENV = 'test';
process.env.FUNCTIONS_EMULATOR = 'true';

// Timeout pour les tests
process.env.JEST_TIMEOUT = '30000';

// Désactiver les logs Firebase pendant les tests
process.env.FIREBASE_CONFIG = JSON.stringify({
  projectId: 'sigma-test-project',
  apiKey: 'fake-api-key',
  authDomain: 'sigma-test-project.firebaseapp.com',
  storageBucket: 'sigma-test-project.appspot.com',
  messagingSenderId: '123456789',
  appId: 'fake-app-id'
});

// Configuration pour supprimer les warnings
process.env.SUPPRESS_NO_CONFIG_WARNING = 'true';
process.env.FIREBASE_EMULATOR_SUITE = 'true';
