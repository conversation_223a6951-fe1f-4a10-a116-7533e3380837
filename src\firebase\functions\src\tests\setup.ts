/**
 * Configuration globale pour les tests Jest
 * Setup et utilitaires communs pour tous les tests
 */

import { initializeApp, getApps, deleteApp } from 'firebase/app';
import { connectAuthEmulator, getAuth } from 'firebase/auth';
import { connectFirestoreEmulator, getFirestore } from 'firebase/firestore';
import { connectFunctionsEmulator, getFunctions } from 'firebase/functions';
import { connectStorageEmulator, getStorage } from 'firebase/storage';

// Configuration Firebase pour les tests
const firebaseConfig = {
  projectId: 'sigma-test-project',
  apiKey: 'fake-api-key',
  authDomain: 'sigma-test-project.firebaseapp.com',
  storageBucket: 'sigma-test-project.appspot.com',
  messagingSenderId: '123456789',
  appId: 'fake-app-id'
};

// Ports des émulateurs
const EMULATOR_CONFIG = {
  auth: { host: 'localhost', port: 9099 },
  firestore: { host: 'localhost', port: 8080 },
  functions: { host: 'localhost', port: 5001 },
  storage: { host: 'localhost', port: 9199 }
};

/**
 * Initialiser Firebase pour les tests
 */
export function initializeTestFirebase() {
  // Nettoyer les apps existantes
  const existingApps = getApps();
  existingApps.forEach(app => deleteApp(app));

  // Initialiser l'app de test
  const app = initializeApp(firebaseConfig, 'test-app');

  // Connecter aux émulateurs
  const auth = getAuth(app);
  const firestore = getFirestore(app);
  const functions = getFunctions(app);
  const storage = getStorage(app);

  // Connecter aux émulateurs seulement si pas déjà connectés
  try {
    connectAuthEmulator(auth, `http://${EMULATOR_CONFIG.auth.host}:${EMULATOR_CONFIG.auth.port}`, {
      disableWarnings: true
    });
  } catch (error) {
    // Émulateur déjà connecté
  }

  try {
    connectFirestoreEmulator(firestore, EMULATOR_CONFIG.firestore.host, EMULATOR_CONFIG.firestore.port);
  } catch (error) {
    // Émulateur déjà connecté
  }

  try {
    connectFunctionsEmulator(functions, EMULATOR_CONFIG.functions.host, EMULATOR_CONFIG.functions.port);
  } catch (error) {
    // Émulateur déjà connecté
  }

  try {
    connectStorageEmulator(storage, EMULATOR_CONFIG.storage.host, EMULATOR_CONFIG.storage.port);
  } catch (error) {
    // Émulateur déjà connecté
  }

  return { app, auth, firestore, functions, storage };
}

/**
 * Nettoyer Firebase après les tests
 */
export async function cleanupTestFirebase() {
  const apps = getApps();
  await Promise.all(apps.map(app => deleteApp(app)));
}

// Configuration globale Jest
beforeAll(async () => {
  // Configurer les timeouts pour les tests d'intégration
  jest.setTimeout(30000);
  
  // Supprimer les warnings des émulateurs
  process.env.FIRESTORE_EMULATOR_HOST = `${EMULATOR_CONFIG.firestore.host}:${EMULATOR_CONFIG.firestore.port}`;
  process.env.FIREBASE_AUTH_EMULATOR_HOST = `${EMULATOR_CONFIG.auth.host}:${EMULATOR_CONFIG.auth.port}`;
  process.env.FIREBASE_STORAGE_EMULATOR_HOST = `${EMULATOR_CONFIG.storage.host}:${EMULATOR_CONFIG.storage.port}`;
  process.env.FUNCTIONS_EMULATOR_HOST = `${EMULATOR_CONFIG.functions.host}:${EMULATOR_CONFIG.functions.port}`;
});

afterAll(async () => {
  await cleanupTestFirebase();
});

// Utilitaires de test exportés
export { EMULATOR_CONFIG, firebaseConfig };

/**
 * Créer un utilisateur de test avec des Custom Claims
 */
export async function createTestUser(
  uid: string,
  email: string,
  displayName: string,
  role: 'admin' | 'regisseur' | 'utilisateur' = 'utilisateur'
) {
  // Cette fonction sera utilisée dans les tests individuels
  // avec l'Admin SDK initialisé dans l'environnement de test
  return {
    uid,
    email,
    displayName,
    role,
    customClaims: { role },
    metadata: {
      creationTime: new Date().toISOString(),
      lastSignInTime: new Date().toISOString()
    }
  };
}

/**
 * Attendre que les émulateurs soient prêts
 */
export async function waitForEmulators(timeout: number = 10000): Promise<void> {
  const startTime = Date.now();
  
  while (Date.now() - startTime < timeout) {
    try {
      // Tenter une connexion simple aux émulateurs
      const response = await fetch(`http://${EMULATOR_CONFIG.firestore.host}:${EMULATOR_CONFIG.firestore.port}`);
      if (response.ok || response.status === 404) {
        return; // Émulateur répond
      }
    } catch (error) {
      // Émulateur pas encore prêt
    }
    
    await new Promise(resolve => setTimeout(resolve, 100));
  }
  
  throw new Error('Timeout: Les émulateurs ne sont pas prêts');
}

/**
 * Générer des données de test aléatoires
 */
export const testDataGenerators = {
  randomEmail: () => `test-${Math.random().toString(36).substr(2, 9)}@example.com`,
  randomUid: () => `test-uid-${Math.random().toString(36).substr(2, 9)}`,
  randomString: (length: number = 10) => Math.random().toString(36).substr(2, length),
  
  createEmpruntData: () => ({
    nom: `Emprunt Test ${testDataGenerators.randomString(5)}`,
    lieu: 'Lieu Test',
    dateDepart: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString(), // Demain
    dateRetour: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString(), // Dans 7 jours
    emprunteur: 'Emprunteur Test',
    statut: 'Pas prêt' as const,
    materiel: []
  }),
  
  createStockData: () => ({
    nom: `Stock Test ${testDataGenerators.randomString(5)}`,
    description: 'Description test',
    categorie: 'Test',
    quantite: Math.floor(Math.random() * 100) + 1,
    seuil: 5,
    unite: 'pièce',
    prix: Math.floor(Math.random() * 1000) / 100,
    estActif: true
  }),
  
  createModuleData: () => ({
    nom: `Module Test ${testDataGenerators.randomString(5)}`,
    description: 'Description module test',
    categorie: 'Test',
    contenu: [
      {
        stockId: testDataGenerators.randomUid(),
        nom: 'Item test',
        quantite: 1
      }
    ],
    estPret: true,
    estActif: true
  })
};

// Matchers Jest personnalisés
declare global {
  namespace jest {
    interface Matchers<R> {
      toBeValidFirebaseTimestamp(): R;
      toHaveValidRole(): R;
    }
  }
}

// Ajouter des matchers personnalisés
expect.extend({
  toBeValidFirebaseTimestamp(received) {
    const pass = received && 
                 typeof received === 'object' && 
                 'seconds' in received && 
                 'nanoseconds' in received;
    
    if (pass) {
      return {
        message: () => `expected ${received} not to be a valid Firebase Timestamp`,
        pass: true,
      };
    } else {
      return {
        message: () => `expected ${received} to be a valid Firebase Timestamp`,
        pass: false,
      };
    }
  },
  
  toHaveValidRole(received) {
    const validRoles = ['admin', 'regisseur', 'utilisateur'];
    const pass = validRoles.includes(received);
    
    if (pass) {
      return {
        message: () => `expected ${received} not to be a valid role`,
        pass: true,
      };
    } else {
      return {
        message: () => `expected ${received} to be one of: ${validRoles.join(', ')}`,
        pass: false,
      };
    }
  }
});
