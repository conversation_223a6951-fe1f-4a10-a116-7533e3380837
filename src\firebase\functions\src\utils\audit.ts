/**
 * Module d'audit et de logging pour SIGMA
 * Centralise tous les logs d'audit et de sécurité
 */

import { getFirestore, FieldValue } from 'firebase-admin/firestore';
import { logger } from 'firebase-functions';
import { AuthenticatedUser } from './auth';

// Types pour les logs d'audit
export interface AuditLog {
  userId: string;
  userEmail: string;
  userRole: string;
  action: string;
  resource?: string;
  resourceId?: string;
  details?: Record<string, any>;
  timestamp: FirebaseFirestore.Timestamp;
  ip?: string;
  userAgent?: string;
  success: boolean;
  errorMessage?: string;
}

// Types d'actions auditées
export enum AuditAction {
  // Authentification
  LOGIN = 'auth.login',
  LOGOUT = 'auth.logout',
  ROLE_CHANGE = 'auth.role_change',
  
  // Emprunts
  EMPRUNT_CREATE = 'emprunt.create',
  EMPRUNT_UPDATE = 'emprunt.update',
  EMPRUNT_DELETE = 'emprunt.delete',
  EMPRUNT_STATUS_CHANGE = 'emprunt.status_change',
  
  // Stocks
  STOCK_CREATE = 'stock.create',
  STOCK_UPDATE = 'stock.update',
  STOCK_DELETE = 'stock.delete',
  STOCK_QUANTITY_ADJUST = 'stock.quantity_adjust',
  
  // Modules
  MODULE_CREATE = 'module.create',
  MODULE_UPDATE = 'module.update',
  MODULE_DELETE = 'module.delete',
  MODULE_DISMANTLE = 'module.dismantle',
  
  // Livraisons
  LIVRAISON_CREATE = 'livraison.create',
  LIVRAISON_UPDATE = 'livraison.update',
  LIVRAISON_DELETE = 'livraison.delete',
  LIVRAISON_STATUS_CHANGE = 'livraison.status_change',
  
  // Administration
  USER_LIST = 'admin.user_list',
  USER_PROFILE_UPDATE = 'admin.user_profile_update',
  SYSTEM_CONFIG_UPDATE = 'admin.system_config_update',
  
  // Sécurité
  UNAUTHORIZED_ACCESS = 'security.unauthorized_access',
  INVALID_INPUT = 'security.invalid_input',
  PERMISSION_DENIED = 'security.permission_denied'
}

/**
 * Enregistrer un log d'audit dans Firestore
 */
export async function logAuditEvent(
  user: AuthenticatedUser,
  action: AuditAction,
  options: {
    resource?: string;
    resourceId?: string;
    details?: Record<string, any>;
    ip?: string;
    userAgent?: string;
    success?: boolean;
    errorMessage?: string;
  } = {}
): Promise<void> {
  try {
    const db = getFirestore();
    
    const auditLog: AuditLog = {
      userId: user.uid,
      userEmail: user.email,
      userRole: user.role,
      action,
      resource: options.resource,
      resourceId: options.resourceId,
      details: options.details,
      timestamp: FieldValue.serverTimestamp() as FirebaseFirestore.Timestamp,
      ip: options.ip,
      userAgent: options.userAgent,
      success: options.success ?? true,
      errorMessage: options.errorMessage
    };

    // Enregistrer dans Firestore
    await db.collection('auditLogs').add(auditLog);

    // Logger aussi dans Cloud Functions pour le monitoring
    logger.info('Audit log enregistré', {
      userId: user.uid,
      action,
      resource: options.resource,
      success: options.success ?? true
    });

  } catch (error) {
    // Ne pas faire échouer l'opération principale si l'audit échoue
    logger.error('Erreur lors de l\'enregistrement du log d\'audit', {
      userId: user.uid,
      action,
      error: error instanceof Error ? error.message : 'Erreur inconnue'
    });
  }
}

/**
 * Enregistrer une tentative d'accès non autorisé
 */
export async function logUnauthorizedAccess(
  userId: string | null,
  action: string,
  details: {
    requiredRole?: string;
    userRole?: string;
    resource?: string;
    ip?: string;
    userAgent?: string;
    errorMessage?: string;
  }
): Promise<void> {
  try {
    const db = getFirestore();
    
    const securityLog = {
      userId: userId || 'anonymous',
      userEmail: details.userRole ? 'unknown' : null,
      userRole: details.userRole || 'none',
      action: AuditAction.UNAUTHORIZED_ACCESS,
      resource: details.resource,
      details: {
        attemptedAction: action,
        requiredRole: details.requiredRole,
        actualRole: details.userRole,
        errorMessage: details.errorMessage
      },
      timestamp: FieldValue.serverTimestamp(),
      ip: details.ip,
      userAgent: details.userAgent,
      success: false,
      errorMessage: details.errorMessage
    };

    await db.collection('auditLogs').add(securityLog);

    // Logger avec niveau WARNING pour alerter
    logger.warn('Tentative d\'accès non autorisé', {
      userId: userId || 'anonymous',
      action,
      requiredRole: details.requiredRole,
      userRole: details.userRole,
      resource: details.resource,
      ip: details.ip
    });

  } catch (error) {
    logger.error('Erreur lors de l\'enregistrement du log de sécurité', {
      userId: userId || 'anonymous',
      action,
      error: error instanceof Error ? error.message : 'Erreur inconnue'
    });
  }
}

/**
 * Enregistrer les changements de données sensibles
 */
export async function logDataChange(
  user: AuthenticatedUser,
  action: AuditAction,
  resourceType: string,
  resourceId: string,
  changes: {
    before?: Record<string, any>;
    after?: Record<string, any>;
    fields?: string[];
  },
  options: {
    ip?: string;
    userAgent?: string;
  } = {}
): Promise<void> {
  await logAuditEvent(user, action, {
    resource: resourceType,
    resourceId,
    details: {
      changes,
      modifiedFields: changes.fields
    },
    ip: options.ip,
    userAgent: options.userAgent,
    success: true
  });
}

/**
 * Enregistrer les erreurs de validation
 */
export async function logValidationError(
  user: AuthenticatedUser | null,
  action: string,
  validationErrors: string[],
  inputData?: any,
  options: {
    ip?: string;
    userAgent?: string;
  } = {}
): Promise<void> {
  if (user) {
    await logAuditEvent(user, AuditAction.INVALID_INPUT, {
      details: {
        attemptedAction: action,
        validationErrors,
        inputData: inputData ? JSON.stringify(inputData) : undefined
      },
      ip: options.ip,
      userAgent: options.userAgent,
      success: false,
      errorMessage: `Validation échouée: ${validationErrors.join(', ')}`
    });
  }

  logger.warn('Erreur de validation', {
    userId: user?.uid || 'anonymous',
    action,
    validationErrors,
    ip: options.ip
  });
}

/**
 * Nettoyer les anciens logs d'audit (à exécuter périodiquement)
 */
export async function cleanupOldAuditLogs(retentionDays: number = 365): Promise<number> {
  try {
    const db = getFirestore();
    const cutoffDate = new Date();
    cutoffDate.setDate(cutoffDate.getDate() - retentionDays);

    const oldLogsQuery = db.collection('auditLogs')
      .where('timestamp', '<', cutoffDate)
      .limit(500); // Traiter par batch pour éviter les timeouts

    const snapshot = await oldLogsQuery.get();
    
    if (snapshot.empty) {
      return 0;
    }

    const batch = db.batch();
    snapshot.docs.forEach(doc => {
      batch.delete(doc.ref);
    });

    await batch.commit();

    logger.info('Nettoyage des logs d\'audit', {
      deletedCount: snapshot.size,
      cutoffDate: cutoffDate.toISOString()
    });

    return snapshot.size;

  } catch (error) {
    logger.error('Erreur lors du nettoyage des logs d\'audit', {
      error: error instanceof Error ? error.message : 'Erreur inconnue'
    });
    throw error;
  }
}

/**
 * Obtenir les statistiques d'audit
 */
export async function getAuditStats(
  startDate: Date,
  endDate: Date
): Promise<{
  totalEvents: number;
  successfulEvents: number;
  failedEvents: number;
  topActions: Array<{ action: string; count: number }>;
  topUsers: Array<{ userId: string; userEmail: string; count: number }>;
}> {
  try {
    const db = getFirestore();
    
    const logsQuery = db.collection('auditLogs')
      .where('timestamp', '>=', startDate)
      .where('timestamp', '<=', endDate);

    const snapshot = await logsQuery.get();
    
    const stats = {
      totalEvents: snapshot.size,
      successfulEvents: 0,
      failedEvents: 0,
      topActions: new Map<string, number>(),
      topUsers: new Map<string, { userEmail: string; count: number }>()
    };

    snapshot.docs.forEach(doc => {
      const data = doc.data() as AuditLog;
      
      if (data.success) {
        stats.successfulEvents++;
      } else {
        stats.failedEvents++;
      }

      // Compter les actions
      const actionCount = stats.topActions.get(data.action) || 0;
      stats.topActions.set(data.action, actionCount + 1);

      // Compter les utilisateurs
      const userStats = stats.topUsers.get(data.userId) || { userEmail: data.userEmail, count: 0 };
      userStats.count++;
      stats.topUsers.set(data.userId, userStats);
    });

    return {
      totalEvents: stats.totalEvents,
      successfulEvents: stats.successfulEvents,
      failedEvents: stats.failedEvents,
      topActions: Array.from(stats.topActions.entries())
        .map(([action, count]) => ({ action, count }))
        .sort((a, b) => b.count - a.count)
        .slice(0, 10),
      topUsers: Array.from(stats.topUsers.entries())
        .map(([userId, data]) => ({ userId, userEmail: data.userEmail, count: data.count }))
        .sort((a, b) => b.count - a.count)
        .slice(0, 10)
    };

  } catch (error) {
    logger.error('Erreur lors du calcul des statistiques d\'audit', {
      error: error instanceof Error ? error.message : 'Erreur inconnue'
    });
    throw error;
  }
}
