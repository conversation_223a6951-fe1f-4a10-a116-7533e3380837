/**
 * Utilitaires d'authentification et de validation des rôles
 * Module centralisé pour la sécurité côté serveur
 */

import { CallableContext } from 'firebase-functions/v1/https';
import { logger } from 'firebase-functions';
import { getAuth } from 'firebase-admin/auth';

// Types des rôles autorisés
export type UserRole = 'admin' | 'regisseur' | 'utilisateur';

// Interface pour les claims personnalisés
export interface CustomClaims {
  role: UserRole;
  [key: string]: any;
}

// Interface pour l'utilisateur authentifié
export interface AuthenticatedUser {
  uid: string;
  email: string;
  role: UserRole;
  displayName?: string;
}

/**
 * Erreur d'authentification personnalisée
 */
export class AuthError extends Error {
  constructor(
    message: string,
    public code: string = 'UNAUTHENTICATED',
    public statusCode: number = 401
  ) {
    super(message);
    this.name = 'AuthError';
  }
}

/**
 * Erreur d'autorisation personnalisée
 */
export class AuthorizationError extends Error {
  constructor(
    message: string,
    public code: string = 'PERMISSION_DENIED',
    public statusCode: number = 403
  ) {
    super(message);
    this.name = 'AuthorizationError';
  }
}

/**
 * Vérifier si l'utilisateur est authentifié
 */
export function requireAuth(context: CallableContext): AuthenticatedUser {
  if (!context.auth) {
    logger.warn('Tentative d\'accès non authentifié', {
      timestamp: new Date().toISOString(),
      ip: context.rawRequest?.ip,
      userAgent: context.rawRequest?.headers['user-agent']
    });
    throw new AuthError('Authentification requise');
  }

  const { uid, token } = context.auth;
  const role = token.role as UserRole;

  if (!role || !['admin', 'regisseur', 'utilisateur'].includes(role)) {
    logger.error('Rôle utilisateur invalide ou manquant', {
      uid,
      role,
      email: token.email,
      timestamp: new Date().toISOString()
    });
    throw new AuthorizationError('Rôle utilisateur invalide');
  }

  return {
    uid,
    email: token.email || '',
    role,
    displayName: token.name
  };
}

/**
 * Vérifier si l'utilisateur a un rôle spécifique
 */
export function requireRole(context: CallableContext, requiredRole: UserRole): AuthenticatedUser {
  const user = requireAuth(context);

  if (!hasRole(user.role, requiredRole)) {
    logger.warn('Tentative d\'accès non autorisé', {
      uid: user.uid,
      email: user.email,
      userRole: user.role,
      requiredRole,
      timestamp: new Date().toISOString(),
      ip: context.rawRequest?.ip,
      functionName: context.rawRequest?.url
    });
    throw new AuthorizationError(`Accès refusé. Rôle requis: ${requiredRole}`);
  }

  return user;
}

/**
 * Vérifier si l'utilisateur a l'un des rôles spécifiés
 */
export function requireAnyRole(context: CallableContext, allowedRoles: UserRole[]): AuthenticatedUser {
  const user = requireAuth(context);

  if (!allowedRoles.some(role => hasRole(user.role, role))) {
    logger.warn('Tentative d\'accès non autorisé - rôles multiples', {
      uid: user.uid,
      email: user.email,
      userRole: user.role,
      allowedRoles,
      timestamp: new Date().toISOString(),
      ip: context.rawRequest?.ip,
      functionName: context.rawRequest?.url
    });
    throw new AuthorizationError(`Accès refusé. Rôles autorisés: ${allowedRoles.join(', ')}`);
  }

  return user;
}

/**
 * Vérifier si un rôle a les permissions d'un autre rôle
 * Hiérarchie : admin > regisseur > utilisateur
 */
export function hasRole(userRole: UserRole, requiredRole: UserRole): boolean {
  const roleHierarchy: Record<UserRole, number> = {
    'utilisateur': 1,
    'regisseur': 2,
    'admin': 3
  };

  return roleHierarchy[userRole] >= roleHierarchy[requiredRole];
}

/**
 * Vérifier si l'utilisateur est propriétaire d'une ressource
 */
export function requireOwnership(context: CallableContext, resourceOwnerId: string): AuthenticatedUser {
  const user = requireAuth(context);

  // Les admins peuvent accéder à toutes les ressources
  if (user.role === 'admin') {
    return user;
  }

  if (user.uid !== resourceOwnerId) {
    logger.warn('Tentative d\'accès à une ressource non autorisée', {
      uid: user.uid,
      email: user.email,
      userRole: user.role,
      resourceOwnerId,
      timestamp: new Date().toISOString(),
      ip: context.rawRequest?.ip
    });
    throw new AuthorizationError('Accès refusé. Vous n\'êtes pas propriétaire de cette ressource.');
  }

  return user;
}

/**
 * Vérifier si l'utilisateur peut modifier une ressource
 * (propriétaire ou rôle supérieur)
 */
export function requireModifyPermission(
  context: CallableContext, 
  resourceOwnerId: string, 
  minimumRole: UserRole = 'regisseur'
): AuthenticatedUser {
  const user = requireAuth(context);

  // Vérifier si l'utilisateur a le rôle minimum requis
  if (hasRole(user.role, minimumRole)) {
    return user;
  }

  // Sinon, vérifier la propriété
  if (user.uid !== resourceOwnerId) {
    logger.warn('Tentative de modification non autorisée', {
      uid: user.uid,
      email: user.email,
      userRole: user.role,
      resourceOwnerId,
      minimumRole,
      timestamp: new Date().toISOString(),
      ip: context.rawRequest?.ip
    });
    throw new AuthorizationError('Accès refusé. Permissions insuffisantes pour modifier cette ressource.');
  }

  return user;
}

/**
 * Logger les tentatives d'accès réussies
 */
export function logSuccessfulAccess(
  user: AuthenticatedUser,
  action: string,
  resource?: string,
  additionalData?: Record<string, any>
): void {
  logger.info('Accès autorisé', {
    uid: user.uid,
    email: user.email,
    role: user.role,
    action,
    resource,
    timestamp: new Date().toISOString(),
    ...additionalData
  });
}

/**
 * Valider et nettoyer les données d'entrée
 */
export function validateAndSanitizeInput<T>(
  data: any,
  validator: (data: any) => T,
  context: CallableContext
): T {
  try {
    return validator(data);
  } catch (error) {
    logger.warn('Données d\'entrée invalides', {
      uid: context.auth?.uid,
      email: context.auth?.token?.email,
      error: error instanceof Error ? error.message : 'Erreur inconnue',
      data: JSON.stringify(data),
      timestamp: new Date().toISOString(),
      ip: context.rawRequest?.ip
    });
    throw new Error('Données d\'entrée invalides');
  }
}

/**
 * Middleware pour wrapper les fonctions avec validation d'authentification
 */
export function withAuth<T extends any[], R>(
  handler: (user: AuthenticatedUser, ...args: T) => Promise<R>,
  requiredRole?: UserRole
) {
  return async (context: CallableContext, ...args: T): Promise<R> => {
    const user = requiredRole ? requireRole(context, requiredRole) : requireAuth(context);
    
    try {
      const result = await handler(user, ...args);
      logSuccessfulAccess(user, handler.name, undefined, { args: args.length });
      return result;
    } catch (error) {
      logger.error('Erreur dans la fonction authentifiée', {
        uid: user.uid,
        email: user.email,
        role: user.role,
        functionName: handler.name,
        error: error instanceof Error ? error.message : 'Erreur inconnue',
        timestamp: new Date().toISOString()
      });
      throw error;
    }
  };
}

/**
 * Vérifier si un utilisateur existe et récupérer ses informations
 */
export async function getUserInfo(uid: string): Promise<{
  uid: string;
  email: string;
  displayName?: string;
  role: UserRole;
  disabled: boolean;
}> {
  try {
    const userRecord = await getAuth().getUser(uid);
    const role = (userRecord.customClaims?.role as UserRole) || 'utilisateur';
    
    return {
      uid: userRecord.uid,
      email: userRecord.email || '',
      displayName: userRecord.displayName,
      role,
      disabled: userRecord.disabled
    };
  } catch (error) {
    logger.error('Erreur lors de la récupération des informations utilisateur', {
      uid,
      error: error instanceof Error ? error.message : 'Erreur inconnue',
      timestamp: new Date().toISOString()
    });
    throw new Error('Utilisateur introuvable');
  }
}

/**
 * Constantes pour les messages d'erreur
 */
export const AUTH_ERRORS = {
  UNAUTHENTICATED: 'Authentification requise',
  INVALID_ROLE: 'Rôle utilisateur invalide',
  PERMISSION_DENIED: 'Permissions insuffisantes',
  RESOURCE_NOT_FOUND: 'Ressource introuvable',
  INVALID_INPUT: 'Données d\'entrée invalides',
  USER_NOT_FOUND: 'Utilisateur introuvable'
} as const;
