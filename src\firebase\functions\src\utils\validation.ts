/**
 * Utilitaires de validation pour les Cloud Functions SIGMA
 * Validation des données métier et des permissions spécifiques
 */

import { z } from 'zod';
import { getFirestore } from 'firebase-admin/firestore';
import { logger } from 'firebase-functions';
import { AuthenticatedUser } from './auth';

// Schémas de validation pour les entités métier

// Validation des emprunts
export const EmpruntSchema = z.object({
  nom: z.string().min(1, 'Nom de l\'emprunt requis').max(100, 'Nom trop long'),
  lieu: z.string().min(1, 'Lieu requis').max(100, 'Lieu trop long'),
  dateDepart: z.string().refine(
    (date) => !isNaN(Date.parse(date)),
    'Date de départ invalide'
  ),
  dateRetour: z.string().refine(
    (date) => !isNaN(Date.parse(date)),
    'Date de retour invalide'
  ),
  emprunteur: z.string().min(1, 'Emprunteur requis').max(100, 'Nom emprunteur trop long'),
  referent: z.string().optional(),
  secteur: z.string().optional(),
  notes: z.string().optional(),
  statut: z.enum(['Pas prêt', 'Prêt', 'Parti', 'Revenu', 'Inventorié'], {
    message: 'Statut invalide'
  }),
  materiel: z.array(z.object({
    stockId: z.string().min(1, 'ID stock requis'),
    nom: z.string().min(1, 'Nom matériel requis'),
    quantite: z.number().int().min(1, 'Quantité doit être positive'),
    quantiteRetournee: z.number().int().min(0, 'Quantité retournée invalide').optional()
  })).optional(),
  estInventorie: z.boolean().optional(),
  estFacture: z.boolean().optional()
}).refine(
  (data) => new Date(data.dateRetour) > new Date(data.dateDepart),
  {
    message: 'La date de retour doit être après la date de départ',
    path: ['dateRetour']
  }
);

// Validation des stocks
export const StockSchema = z.object({
  nom: z.string().min(1, 'Nom de l\'article requis').max(100, 'Nom trop long'),
  description: z.string().optional(),
  categorie: z.string().min(1, 'Catégorie requise').max(50, 'Catégorie trop longue'),
  quantite: z.number().int().min(0, 'Quantité doit être positive ou nulle'),
  seuil: z.number().int().min(0, 'Seuil doit être positif ou nul'),
  unite: z.string().min(1, 'Unité requise').max(20, 'Unité trop longue'),
  prix: z.number().min(0, 'Prix doit être positif ou nul').optional(),
  fournisseur: z.string().optional(),
  reference: z.string().optional(),
  emplacement: z.string().optional(),
  estActif: z.boolean().optional()
});

// Validation des modules
export const ModuleSchema = z.object({
  nom: z.string().min(1, 'Nom du module requis').max(100, 'Nom trop long'),
  description: z.string().optional(),
  categorie: z.string().min(1, 'Catégorie requise').max(50, 'Catégorie trop longue'),
  contenu: z.array(z.object({
    stockId: z.string().min(1, 'ID stock requis'),
    nom: z.string().min(1, 'Nom matériel requis'),
    quantite: z.number().int().min(1, 'Quantité doit être positive')
  })).min(1, 'Le module doit contenir au moins un élément'),
  estPret: z.boolean().optional(),
  estActif: z.boolean().optional(),
  notes: z.string().optional()
});

// Validation des livraisons
export const LivraisonSchema = z.object({
  empruntRef: z.string().min(1, 'Référence emprunt requise'),
  type: z.enum(['aller', 'retour'], {
    message: 'Type de livraison invalide'
  }),
  datePrevu: z.string().refine(
    (date) => !isNaN(Date.parse(date)),
    'Date prévue invalide'
  ),
  adresse: z.object({
    rue: z.string().min(1, 'Rue requise'),
    ville: z.string().min(1, 'Ville requise'),
    codePostal: z.string().min(5, 'Code postal invalide').max(10, 'Code postal trop long'),
    pays: z.string().min(1, 'Pays requis').optional()
  }),
  contact: z.object({
    nom: z.string().min(1, 'Nom contact requis'),
    telephone: z.string().optional(),
    email: z.string().email('Email invalide').optional()
  }),
  statut: z.enum(['planifiee', 'en_cours', 'livree', 'annulee'], {
    message: 'Statut de livraison invalide'
  }),
  transporteur: z.string().optional(),
  numeroSuivi: z.string().optional(),
  notes: z.string().optional()
});

/**
 * Vérifier si un emprunt existe et est accessible par l'utilisateur
 */
export async function validateEmpruntAccess(
  empruntId: string,
  user: AuthenticatedUser,
  requireOwnership: boolean = false
): Promise<FirebaseFirestore.DocumentSnapshot> {
  const db = getFirestore();
  const empruntDoc = await db.collection('emprunts').doc(empruntId).get();

  if (!empruntDoc.exists) {
    throw new Error('Emprunt introuvable');
  }

  const empruntData = empruntDoc.data()!;

  // Vérifier la propriété si requis
  if (requireOwnership && user.role !== 'admin' && user.role !== 'regisseur') {
    if (empruntData.createdBy !== user.uid) {
      throw new Error('Accès refusé. Vous n\'êtes pas propriétaire de cet emprunt.');
    }
  }

  return empruntDoc;
}

/**
 * Vérifier si un stock existe et a suffisamment de quantité
 */
export async function validateStockAvailability(
  stockId: string,
  quantiteRequise: number
): Promise<FirebaseFirestore.DocumentSnapshot> {
  const db = getFirestore();
  const stockDoc = await db.collection('stocks').doc(stockId).get();

  if (!stockDoc.exists) {
    throw new Error(`Stock ${stockId} introuvable`);
  }

  const stockData = stockDoc.data()!;

  if (!stockData.estActif) {
    throw new Error(`Stock ${stockData.nom} inactif`);
  }

  if (stockData.quantite < quantiteRequise) {
    throw new Error(`Stock insuffisant pour ${stockData.nom}. Disponible: ${stockData.quantite}, Requis: ${quantiteRequise}`);
  }

  return stockDoc;
}

/**
 * Vérifier si un module existe et est disponible
 */
export async function validateModuleAvailability(
  moduleId: string
): Promise<FirebaseFirestore.DocumentSnapshot> {
  const db = getFirestore();
  const moduleDoc = await db.collection('modules').doc(moduleId).get();

  if (!moduleDoc.exists) {
    throw new Error(`Module ${moduleId} introuvable`);
  }

  const moduleData = moduleDoc.data()!;

  if (!moduleData.estActif) {
    throw new Error(`Module ${moduleData.nom} inactif`);
  }

  if (!moduleData.estPret) {
    throw new Error(`Module ${moduleData.nom} non prêt`);
  }

  return moduleDoc;
}

/**
 * Valider les dates d'emprunt
 */
export function validateEmpruntDates(dateDepart: string, dateRetour: string): void {
  const depart = new Date(dateDepart);
  const retour = new Date(dateRetour);
  const maintenant = new Date();

  if (depart < maintenant) {
    throw new Error('La date de départ ne peut pas être dans le passé');
  }

  if (retour <= depart) {
    throw new Error('La date de retour doit être après la date de départ');
  }

  const dureeMax = 365 * 24 * 60 * 60 * 1000; // 1 an en millisecondes
  if (retour.getTime() - depart.getTime() > dureeMax) {
    throw new Error('La durée d\'emprunt ne peut pas dépasser 1 an');
  }
}

/**
 * Valider les transitions de statut d'emprunt
 */
export function validateEmpruntStatusTransition(
  currentStatus: string,
  newStatus: string,
  userRole: string
): void {
  const validTransitions: Record<string, string[]> = {
    'Pas prêt': ['Prêt', 'Pas prêt'], // Peut rester en "Pas prêt"
    'Prêt': ['Parti', 'Pas prêt'], // Peut revenir en "Pas prêt" si problème
    'Parti': ['Revenu'],
    'Revenu': ['Inventorié'],
    'Inventorié': [] // État final
  };

  if (!validTransitions[currentStatus]?.includes(newStatus)) {
    throw new Error(`Transition de statut invalide: ${currentStatus} → ${newStatus}`);
  }

  // Seuls les régisseurs et admins peuvent faire certaines transitions
  const restrictedTransitions = ['Prêt', 'Parti', 'Revenu', 'Inventorié'];
  if (restrictedTransitions.includes(newStatus) && !['admin', 'regisseur'].includes(userRole)) {
    throw new Error(`Seuls les régisseurs peuvent définir le statut "${newStatus}"`);
  }
}

/**
 * Logger les validations échouées (version simple)
 */
export function logSimpleValidationError(
  error: Error,
  context: {
    userId: string;
    action: string;
    data?: any;
  }
): void {
  logger.warn('Erreur de validation', {
    userId: context.userId,
    action: context.action,
    error: error.message,
    data: context.data ? JSON.stringify(context.data) : undefined,
    timestamp: new Date().toISOString()
  });
}

/**
 * Nettoyer et normaliser les données d'entrée
 */
export function sanitizeInput(data: any): any {
  if (typeof data === 'string') {
    return data.trim();
  }
  
  if (Array.isArray(data)) {
    return data.map(sanitizeInput);
  }
  
  if (data && typeof data === 'object') {
    const sanitized: any = {};
    for (const [key, value] of Object.entries(data)) {
      sanitized[key] = sanitizeInput(value);
    }
    return sanitized;
  }
  
  return data;
}
