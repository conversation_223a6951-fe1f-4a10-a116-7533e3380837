/**
 * Tests d'intégration end-to-end pour le flux d'authentification SIGMA
 * Teste le flux complet : connexion → assignation rôle → accès ressources → déconnexion
 */

const { initializeTestEnvironment, assertFails, assertSucceeds } = require('@firebase/rules-unit-testing');
const { signInWithCustomToken, signOut, getAuth } = require('firebase/auth');
const { doc, getDoc, setDoc, updateDoc, collection, addDoc, getDocs, query, where } = require('firebase/firestore');
const { ref, uploadBytes, getDownloadURL, getMetadata } = require('firebase/storage');
const { httpsCallable, getFunctions } = require('firebase/functions');

// Configuration des tests
const PROJECT_ID = 'sigma-integration-test';

describe('Tests d\'Intégration End-to-End - Flux d\'Authentification SIGMA', () => {
  let testEnv;
  let adminAuth;
  let adminFirestore;

  // Scénarios de test avec différents utilisateurs
  const testScenarios = {
    newUser: {
      uid: 'new-user-uid',
      email: '<EMAIL>',
      displayName: 'Nouvel Utilisateur',
      expectedInitialRole: 'utilisateur'
    },
    promotedUser: {
      uid: 'promoted-user-uid',
      email: '<EMAIL>',
      displayName: 'Utilisateur Promu',
      initialRole: 'utilisateur',
      promotedRole: 'regisseur'
    },
    admin: {
      uid: 'admin-uid',
      email: '<EMAIL>',
      displayName: 'Administrateur',
      role: 'admin'
    }
  };

  beforeAll(async () => {
    testEnv = await initializeTestEnvironment({
      projectId: PROJECT_ID,
      auth: {
        host: 'localhost',
        port: 9099
      },
      firestore: {
        host: 'localhost',
        port: 8080,
        rules: require('fs').readFileSync('firestore.rules', 'utf8')
      },
      storage: {
        host: 'localhost',
        port: 9199,
        rules: require('fs').readFileSync('storage.rules', 'utf8')
      },
      functions: {
        host: 'localhost',
        port: 5001
      }
    });

    // Obtenir les services admin pour la configuration
    adminAuth = testEnv.authenticatedContext('admin-setup', { role: 'admin' }).auth();
    adminFirestore = testEnv.authenticatedContext('admin-setup', { role: 'admin' }).firestore();
  });

  afterAll(async () => {
    await testEnv.cleanup();
  });

  beforeEach(async () => {
    await testEnv.clearFirestore();
    await testEnv.clearStorage();
  });

  /**
   * Utilitaire pour créer un token personnalisé
   */
  async function createCustomToken(uid, claims = {}) {
    // Dans un vrai test, ceci serait fait via l'Admin SDK
    // Ici on simule avec l'environnement de test
    return testEnv.authenticatedContext(uid, claims);
  }

  /**
   * Utilitaire pour créer des données de test
   */
  function createTestData() {
    return {
      emprunt: {
        nom: 'Emprunt Test E2E',
        lieu: 'Lieu Test',
        dateDepart: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString(),
        dateRetour: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString(),
        emprunteur: 'Emprunteur Test',
        statut: 'Pas prêt',
        materiel: []
      },
      stock: {
        nom: 'Stock Test E2E',
        description: 'Description test',
        categorie: 'Test',
        quantite: 10,
        seuil: 2,
        unite: 'pièce',
        estActif: true
      },
      module: {
        nom: 'Module Test E2E',
        description: 'Description module',
        categorie: 'Test',
        contenu: [{ stockId: 'stock-1', nom: 'Item', quantite: 1 }],
        estPret: true,
        estActif: true
      }
    };
  }

  describe('Flux 1: Création d\'un nouvel utilisateur', () => {
    it('Doit créer un utilisateur avec le rôle par défaut et les permissions appropriées', async () => {
      const { newUser } = testScenarios;
      
      // 1. Simuler la création d'un nouvel utilisateur
      const userContext = await createCustomToken(newUser.uid, { 
        role: newUser.expectedInitialRole,
        email: newUser.email 
      });
      
      // 2. Créer le document utilisateur (simule onUserCreate trigger)
      await testEnv.withSecurityRulesDisabled(async (context) => {
        await setDoc(doc(context.firestore(), 'users', newUser.uid), {
          email: newUser.email,
          displayName: newUser.displayName,
          role: newUser.expectedInitialRole,
          isActive: true,
          createdAt: new Date(),
          preferences: {
            language: 'fr',
            theme: 'light',
            notifications: {
              email: true,
              browser: true,
              emprunts: true,
              stocks: true,
              livraisons: true
            }
          },
          stats: {
            empruntsCount: 0,
            lastActivityAt: new Date()
          }
        });
      });

      // 3. Vérifier que l'utilisateur peut lire son propre profil
      const userFirestore = userContext.firestore();
      const userDoc = await getDoc(doc(userFirestore, 'users', newUser.uid));
      expect(userDoc.exists()).toBe(true);
      expect(userDoc.data().role).toBe(newUser.expectedInitialRole);

      // 4. Vérifier les permissions de base (lecture des collections publiques)
      const testData = createTestData();
      
      // Créer des données de test
      await testEnv.withSecurityRulesDisabled(async (context) => {
        await setDoc(doc(context.firestore(), 'stocks', 'stock-1'), testData.stock);
        await setDoc(doc(context.firestore(), 'modules', 'module-1'), testData.module);
      });

      // L'utilisateur peut lire les stocks et modules
      await assertSucceeds(getDoc(doc(userFirestore, 'stocks', 'stock-1')));
      await assertSucceeds(getDoc(doc(userFirestore, 'modules', 'module-1')));

      // 5. Vérifier que l'utilisateur peut créer un emprunt
      await assertSucceeds(addDoc(collection(userFirestore, 'emprunts'), {
        ...testData.emprunt,
        createdBy: newUser.uid,
        createdAt: new Date(),
        updatedAt: new Date()
      }));

      // 6. Vérifier que l'utilisateur ne peut pas modifier les stocks
      await assertFails(updateDoc(doc(userFirestore, 'stocks', 'stock-1'), {
        quantite: 5
      }));

      console.log('✅ Flux 1 réussi: Nouvel utilisateur créé avec permissions appropriées');
    });
  });

  describe('Flux 2: Promotion d\'utilisateur par un admin', () => {
    it('Doit permettre à un admin de promouvoir un utilisateur et vérifier les nouvelles permissions', async () => {
      const { promotedUser, admin } = testScenarios;
      
      // 1. Créer l'utilisateur initial avec le rôle de base
      const initialUserContext = await createCustomToken(promotedUser.uid, { 
        role: promotedUser.initialRole,
        email: promotedUser.email 
      });
      
      // 2. Créer l'admin
      const adminContext = await createCustomToken(admin.uid, { 
        role: admin.role,
        email: admin.email 
      });

      // 3. Créer les documents utilisateurs
      await testEnv.withSecurityRulesDisabled(async (context) => {
        await setDoc(doc(context.firestore(), 'users', promotedUser.uid), {
          email: promotedUser.email,
          displayName: promotedUser.displayName,
          role: promotedUser.initialRole,
          isActive: true,
          createdAt: new Date()
        });
        
        await setDoc(doc(context.firestore(), 'users', admin.uid), {
          email: admin.email,
          displayName: admin.displayName,
          role: admin.role,
          isActive: true,
          createdAt: new Date()
        });
      });

      // 4. Vérifier les permissions initiales (utilisateur normal)
      const userFirestore = initialUserContext.firestore();
      const testData = createTestData();
      
      await testEnv.withSecurityRulesDisabled(async (context) => {
        await setDoc(doc(context.firestore(), 'stocks', 'stock-1'), testData.stock);
      });

      // L'utilisateur ne peut pas modifier les stocks initialement
      await assertFails(updateDoc(doc(userFirestore, 'stocks', 'stock-1'), {
        quantite: 5
      }));

      // 5. Admin promeut l'utilisateur
      const adminFunctions = getFunctions(adminContext.app);
      const setUserRole = httpsCallable(adminFunctions, 'setUserRole');
      
      const promotionResult = await setUserRole({
        userId: promotedUser.uid,
        role: promotedUser.promotedRole,
        reason: 'Promotion pour test E2E'
      });

      expect(promotionResult.data.success).toBe(true);
      expect(promotionResult.data.newRole).toBe(promotedUser.promotedRole);

      // 6. Créer un nouveau contexte avec le rôle mis à jour
      const promotedUserContext = await createCustomToken(promotedUser.uid, { 
        role: promotedUser.promotedRole,
        email: promotedUser.email 
      });
      
      const promotedUserFirestore = promotedUserContext.firestore();

      // 7. Vérifier les nouvelles permissions (régisseur)
      // Le régisseur peut maintenant modifier les stocks
      await assertSucceeds(updateDoc(doc(promotedUserFirestore, 'stocks', 'stock-1'), {
        quantite: 8
      }));

      // 8. Vérifier que l'historique a été enregistré
      const adminFirestore = adminContext.firestore();
      const historyQuery = query(
        collection(adminFirestore, 'roleHistory'),
        where('userId', '==', promotedUser.uid)
      );
      const historySnapshot = await getDocs(historyQuery);
      
      expect(historySnapshot.size).toBe(1);
      const historyData = historySnapshot.docs[0].data();
      expect(historyData.newRole).toBe(promotedUser.promotedRole);
      expect(historyData.previousRole).toBe(promotedUser.initialRole);

      console.log('✅ Flux 2 réussi: Utilisateur promu avec nouvelles permissions');
    });
  });

  describe('Flux 3: Accès aux ressources selon les rôles', () => {
    it('Doit valider l\'accès différentiel aux ressources selon les rôles', async () => {
      const { admin } = testScenarios;
      const regisseur = { uid: 'regisseur-test', role: 'regisseur', email: '<EMAIL>' };
      const utilisateur = { uid: 'user-test', role: 'utilisateur', email: '<EMAIL>' };

      // 1. Créer les contextes pour chaque rôle
      const adminContext = await createCustomToken(admin.uid, admin);
      const regisseurContext = await createCustomToken(regisseur.uid, regisseur);
      const userContext = await createCustomToken(utilisateur.uid, utilisateur);

      // 2. Créer des données de test
      const testData = createTestData();
      
      await testEnv.withSecurityRulesDisabled(async (context) => {
        await setDoc(doc(context.firestore(), 'stocks', 'stock-1'), testData.stock);
        await setDoc(doc(context.firestore(), 'emprunts', 'emprunt-1'), {
          ...testData.emprunt,
          createdBy: utilisateur.uid
        });
      });

      // 3. Tests d'accès Firestore
      const adminFirestore = adminContext.firestore();
      const regisseurFirestore = regisseurContext.firestore();
      const userFirestore = userContext.firestore();

      // Tous peuvent lire les stocks
      await assertSucceeds(getDoc(doc(adminFirestore, 'stocks', 'stock-1')));
      await assertSucceeds(getDoc(doc(regisseurFirestore, 'stocks', 'stock-1')));
      await assertSucceeds(getDoc(doc(userFirestore, 'stocks', 'stock-1')));

      // Seuls admin/régisseur peuvent modifier les stocks
      await assertSucceeds(updateDoc(doc(adminFirestore, 'stocks', 'stock-1'), { quantite: 15 }));
      await assertSucceeds(updateDoc(doc(regisseurFirestore, 'stocks', 'stock-1'), { quantite: 12 }));
      await assertFails(updateDoc(doc(userFirestore, 'stocks', 'stock-1'), { quantite: 10 }));

      // 4. Tests d'accès Storage
      const adminStorage = adminContext.storage();
      const regisseurStorage = regisseurContext.storage();
      const userStorage = userContext.storage();

      const testFile = new Uint8Array([0xFF, 0xD8, 0xFF, 0xE0]); // Header JPEG

      // Utilisateur peut uploader son avatar
      await assertSucceeds(uploadBytes(ref(userStorage, `avatars/${utilisateur.uid}/profile.jpg`), testFile));

      // Seuls admin/régisseur peuvent uploader des documents
      await assertSucceeds(uploadBytes(ref(adminStorage, 'documents/admin-doc.pdf'), testFile));
      await assertSucceeds(uploadBytes(ref(regisseurStorage, 'documents/regisseur-doc.pdf'), testFile));
      await assertFails(uploadBytes(ref(userStorage, 'documents/user-doc.pdf'), testFile));

      // 5. Tests d'accès aux fonctions
      const adminFunctions = getFunctions(adminContext.app);
      const userFunctions = getFunctions(userContext.app);

      const listUsers = httpsCallable(adminFunctions, 'listUsers');
      const listUsersUser = httpsCallable(userFunctions, 'listUsers');

      // Admin peut lister les utilisateurs
      await assertSucceeds(listUsers({ limit: 10 }));

      // Utilisateur normal ne peut pas lister les utilisateurs
      await assertFails(listUsersUser({ limit: 10 }));

      console.log('✅ Flux 3 réussi: Accès différentiel validé selon les rôles');
    });
  });

  describe('Flux 4: Tentatives d\'accès non autorisé', () => {
    it('Doit bloquer toutes les tentatives d\'accès non autorisé', async () => {
      const utilisateur = { uid: 'malicious-user', role: 'utilisateur', email: '<EMAIL>' };
      const userContext = await createCustomToken(utilisateur.uid, utilisateur);

      // 1. Créer des données sensibles
      await testEnv.withSecurityRulesDisabled(async (context) => {
        await setDoc(doc(context.firestore(), 'users', 'other-user'), {
          email: '<EMAIL>',
          role: 'admin',
          sensitiveData: 'secret'
        });
        
        await setDoc(doc(context.firestore(), 'auditLogs', 'log-1'), {
          userId: 'admin',
          action: 'sensitive-action',
          timestamp: new Date()
        });
      });

      const userFirestore = userContext.firestore();
      const userStorage = userContext.storage();
      const userFunctions = getFunctions(userContext.app);

      // 2. Tentatives d'accès non autorisé Firestore
      
      // Ne peut pas lire le profil d'un autre utilisateur
      await assertFails(getDoc(doc(userFirestore, 'users', 'other-user')));
      
      // Ne peut pas lire les logs d'audit
      await assertFails(getDoc(doc(userFirestore, 'auditLogs', 'log-1')));
      
      // Ne peut pas modifier son propre rôle
      await assertFails(updateDoc(doc(userFirestore, 'users', utilisateur.uid), {
        role: 'admin'
      }));

      // 3. Tentatives d'accès non autorisé Storage
      
      // Ne peut pas accéder aux fichiers admin
      await assertFails(getMetadata(ref(userStorage, 'admin/config.json')));
      
      // Ne peut pas uploader dans le dossier admin
      const testFile = new Uint8Array([1, 2, 3, 4]);
      await assertFails(uploadBytes(ref(userStorage, 'admin/malicious.txt'), testFile));
      
      // Ne peut pas accéder aux avatars d'autres utilisateurs
      await assertFails(uploadBytes(ref(userStorage, 'avatars/other-user/profile.jpg'), testFile));

      // 4. Tentatives d'accès non autorisé Functions
      
      const setUserRole = httpsCallable(userFunctions, 'setUserRole');
      const listUsers = httpsCallable(userFunctions, 'listUsers');
      
      // Ne peut pas changer les rôles
      await assertFails(setUserRole({
        userId: 'other-user',
        role: 'utilisateur'
      }));
      
      // Ne peut pas lister les utilisateurs
      await assertFails(listUsers({ limit: 10 }));

      console.log('✅ Flux 4 réussi: Toutes les tentatives d\'accès non autorisé bloquées');
    });
  });

  describe('Flux 5: Déconnexion et nettoyage', () => {
    it('Doit gérer la déconnexion et empêcher l\'accès après déconnexion', async () => {
      const utilisateur = { uid: 'logout-user', role: 'utilisateur', email: '<EMAIL>' };
      
      // 1. Connexion initiale
      let userContext = await createCustomToken(utilisateur.uid, utilisateur);
      let userFirestore = userContext.firestore();

      // 2. Créer des données accessibles
      await testEnv.withSecurityRulesDisabled(async (context) => {
        await setDoc(doc(context.firestore(), 'stocks', 'stock-1'), {
          nom: 'Stock Test',
          quantite: 10,
          estActif: true
        });
      });

      // 3. Vérifier l'accès initial
      await assertSucceeds(getDoc(doc(userFirestore, 'stocks', 'stock-1')));

      // 4. Simuler la déconnexion (contexte non authentifié)
      const unauthenticatedContext = testEnv.unauthenticatedContext();
      const anonFirestore = unauthenticatedContext.firestore();
      const anonStorage = unauthenticatedContext.storage();

      // 5. Vérifier que l'accès est bloqué après déconnexion
      await assertFails(getDoc(doc(anonFirestore, 'stocks', 'stock-1')));
      await assertFails(getDoc(doc(anonFirestore, 'users', utilisateur.uid)));
      
      // Storage aussi bloqué
      const testFile = new Uint8Array([1, 2, 3, 4]);
      await assertFails(uploadBytes(ref(anonStorage, 'public/test.jpg'), testFile));

      console.log('✅ Flux 5 réussi: Déconnexion et blocage d\'accès validés');
    });
  });

  describe('Flux 6: Audit et logging', () => {
    it('Doit enregistrer les actions importantes dans les logs d\'audit', async () => {
      const admin = { uid: 'audit-admin', role: 'admin', email: '<EMAIL>' };
      const utilisateur = { uid: 'audit-user', role: 'utilisateur', email: '<EMAIL>' };

      const adminContext = await createCustomToken(admin.uid, admin);
      const userContext = await createCustomToken(utilisateur.uid, utilisateur);

      // 1. Actions qui doivent être auditées
      const adminFunctions = getFunctions(adminContext.app);
      const setUserRole = httpsCallable(adminFunctions, 'setUserRole');

      // Créer l'utilisateur
      await testEnv.withSecurityRulesDisabled(async (context) => {
        await setDoc(doc(context.firestore(), 'users', utilisateur.uid), {
          email: utilisateur.email,
          role: utilisateur.role,
          isActive: true
        });
      });

      // 2. Changement de rôle (doit être audité)
      await setUserRole({
        userId: utilisateur.uid,
        role: 'regisseur',
        reason: 'Test audit'
      });

      // 3. Vérifier que les logs d'audit ont été créés
      const adminFirestore = adminContext.firestore();
      
      // Vérifier l'historique des rôles
      const roleHistoryQuery = query(
        collection(adminFirestore, 'roleHistory'),
        where('userId', '==', utilisateur.uid)
      );
      const roleHistorySnapshot = await getDocs(roleHistoryQuery);
      expect(roleHistorySnapshot.size).toBeGreaterThan(0);

      // 4. Créer un emprunt (doit être audité)
      const userFirestore = userContext.firestore();
      await addDoc(collection(userFirestore, 'emprunts'), {
        nom: 'Emprunt Audit Test',
        lieu: 'Test',
        dateDepart: new Date().toISOString(),
        dateRetour: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString(),
        emprunteur: 'Test User',
        statut: 'Pas prêt',
        createdBy: utilisateur.uid,
        createdAt: new Date()
      });

      console.log('✅ Flux 6 réussi: Actions auditées et logs créés');
    });
  });
});
