/**
 * Tests de sécurité pour les règles Firebase Storage SIGMA
 * Valide l'accès aux fichiers selon les rôles et permissions
 */

const { initializeTestEnvironment, assertFails, assertSucceeds } = require('@firebase/rules-unit-testing');
const { ref, uploadBytes, getDownloadURL, deleteObject, getMetadata } = require('firebase/storage');

// Configuration des tests
const PROJECT_ID = 'sigma-storage-rules-test';

describe('Tests de Sécurité Storage - Règles SIGMA', () => {
  let testEnv;

  // Utilisateurs de test avec différents rôles
  const users = {
    admin: { uid: 'admin-uid', role: 'admin', email: '<EMAIL>' },
    regisseur: { uid: 'regisseur-uid', role: 'regisseur', email: '<EMAIL>' },
    utilisateur: { uid: 'user-uid', role: 'utilisateur', email: '<EMAIL>' },
    otherUser: { uid: 'other-uid', role: 'utilisateur', email: '<EMAIL>' },
    anonymous: null
  };

  // Données de test pour les fichiers
  const testFiles = {
    image: new Uint8Array([0xFF, 0xD8, 0xFF, 0xE0]), // Header JPEG
    pdf: new Uint8Array([0x25, 0x50, 0x44, 0x46]), // Header PDF
    text: new TextEncoder().encode('Test file content'),
    largeFile: new Uint8Array(11 * 1024 * 1024), // 11MB (dépasse la limite)
    invalidType: new TextEncoder().encode('<?php echo "hack"; ?>') // Type non autorisé
  };

  beforeAll(async () => {
    testEnv = await initializeTestEnvironment({
      projectId: PROJECT_ID,
      storage: {
        rules: require('fs').readFileSync('storage.rules', 'utf8'),
        host: 'localhost',
        port: 9199
      }
    });
  });

  afterAll(async () => {
    await testEnv.cleanup();
  });

  beforeEach(async () => {
    await testEnv.clearStorage();
  });

  /**
   * Utilitaire pour créer un contexte d'authentification
   */
  function getAuthContext(user) {
    if (!user) return testEnv.unauthenticatedContext();
    return testEnv.authenticatedContext(user.uid, { role: user.role, email: user.email });
  }

  describe('Dossier: avatars/', () => {
    it('Utilisateur peut uploader son propre avatar', async () => {
      const userStorage = getAuthContext(users.utilisateur).storage();
      const avatarRef = ref(userStorage, `avatars/${users.utilisateur.uid}/profile.jpg`);
      
      await assertSucceeds(uploadBytes(avatarRef, testFiles.image));
    });

    it('Utilisateur ne peut pas uploader l\'avatar d\'un autre utilisateur', async () => {
      const userStorage = getAuthContext(users.utilisateur).storage();
      const otherAvatarRef = ref(userStorage, `avatars/${users.otherUser.uid}/profile.jpg`);
      
      await assertFails(uploadBytes(otherAvatarRef, testFiles.image));
    });

    it('Admin peut uploader n\'importe quel avatar', async () => {
      const adminStorage = getAuthContext(users.admin).storage();
      const avatarRef = ref(adminStorage, `avatars/${users.utilisateur.uid}/profile.jpg`);
      
      await assertSucceeds(uploadBytes(avatarRef, testFiles.image));
    });

    it('Utilisateur anonyme ne peut pas uploader d\'avatar', async () => {
      const anonStorage = getAuthContext(users.anonymous).storage();
      const avatarRef = ref(anonStorage, `avatars/${users.utilisateur.uid}/profile.jpg`);
      
      await assertFails(uploadBytes(avatarRef, testFiles.image));
    });

    it('Tous les utilisateurs authentifiés peuvent lire les avatars', async () => {
      const adminStorage = getAuthContext(users.admin).storage();
      const userStorage = getAuthContext(users.utilisateur).storage();
      const avatarRef = ref(adminStorage, `avatars/${users.utilisateur.uid}/profile.jpg`);
      
      // Admin upload l'avatar
      await uploadBytes(avatarRef, testFiles.image);
      
      // Utilisateur peut le lire
      const userAvatarRef = ref(userStorage, `avatars/${users.utilisateur.uid}/profile.jpg`);
      await assertSucceeds(getMetadata(userAvatarRef));
    });

    it('Fichier avatar doit être une image valide', async () => {
      const userStorage = getAuthContext(users.utilisateur).storage();
      const avatarRef = ref(userStorage, `avatars/${users.utilisateur.uid}/profile.txt`);
      
      // Fichier texte non autorisé pour avatar
      await assertFails(uploadBytes(avatarRef, testFiles.text));
    });

    it('Avatar ne peut pas dépasser 5MB', async () => {
      const userStorage = getAuthContext(users.utilisateur).storage();
      const avatarRef = ref(userStorage, `avatars/${users.utilisateur.uid}/large.jpg`);
      
      await assertFails(uploadBytes(avatarRef, testFiles.largeFile));
    });
  });

  describe('Dossier: documents/', () => {
    it('Tous les utilisateurs authentifiés peuvent lire les documents', async () => {
      const adminStorage = getAuthContext(users.admin).storage();
      const userStorage = getAuthContext(users.utilisateur).storage();
      const docRef = ref(adminStorage, 'documents/manual.pdf');
      
      // Admin upload le document
      await uploadBytes(docRef, testFiles.pdf);
      
      // Utilisateur peut le lire
      const userDocRef = ref(userStorage, 'documents/manual.pdf');
      await assertSucceeds(getMetadata(userDocRef));
    });

    it('Seuls admin/régisseur peuvent uploader des documents', async () => {
      const userStorage = getAuthContext(users.utilisateur).storage();
      const adminStorage = getAuthContext(users.admin).storage();
      const regisseurStorage = getAuthContext(users.regisseur).storage();
      
      const docRef = 'documents/new-manual.pdf';
      
      // Utilisateur normal ne peut pas uploader
      await assertFails(uploadBytes(ref(userStorage, docRef), testFiles.pdf));
      
      // Admin peut uploader
      await assertSucceeds(uploadBytes(ref(adminStorage, docRef), testFiles.pdf));
      
      // Régisseur peut uploader
      await assertSucceeds(uploadBytes(ref(regisseurStorage, 'documents/regisseur-doc.pdf'), testFiles.pdf));
    });

    it('Documents doivent être des PDF ou images', async () => {
      const adminStorage = getAuthContext(users.admin).storage();
      
      // PDF autorisé
      await assertSucceeds(uploadBytes(ref(adminStorage, 'documents/manual.pdf'), testFiles.pdf));
      
      // Image autorisée
      await assertSucceeds(uploadBytes(ref(adminStorage, 'documents/diagram.jpg'), testFiles.image));
      
      // Fichier texte non autorisé
      await assertFails(uploadBytes(ref(adminStorage, 'documents/readme.txt'), testFiles.text));
    });

    it('Utilisateur anonyme ne peut pas accéder aux documents', async () => {
      const anonStorage = getAuthContext(users.anonymous).storage();
      const docRef = ref(anonStorage, 'documents/manual.pdf');
      
      await assertFails(getMetadata(docRef));
    });
  });

  describe('Dossier: proofs/', () => {
    it('Utilisateur peut uploader des preuves pour ses emprunts', async () => {
      const userStorage = getAuthContext(users.utilisateur).storage();
      const proofRef = ref(userStorage, `proofs/${users.utilisateur.uid}/emprunt-1/photo1.jpg`);
      
      await assertSucceeds(uploadBytes(proofRef, testFiles.image));
    });

    it('Utilisateur ne peut pas uploader des preuves pour les emprunts d\'autres utilisateurs', async () => {
      const userStorage = getAuthContext(users.utilisateur).storage();
      const otherProofRef = ref(userStorage, `proofs/${users.otherUser.uid}/emprunt-1/photo1.jpg`);
      
      await assertFails(uploadBytes(otherProofRef, testFiles.image));
    });

    it('Admin/régisseur peuvent uploader des preuves pour n\'importe quel emprunt', async () => {
      const regisseurStorage = getAuthContext(users.regisseur).storage();
      const proofRef = ref(regisseurStorage, `proofs/${users.utilisateur.uid}/emprunt-1/inspection.jpg`);
      
      await assertSucceeds(uploadBytes(proofRef, testFiles.image));
    });

    it('Preuves doivent être des images ou PDF', async () => {
      const userStorage = getAuthContext(users.utilisateur).storage();
      
      // Image autorisée
      await assertSucceeds(uploadBytes(
        ref(userStorage, `proofs/${users.utilisateur.uid}/emprunt-1/photo.jpg`), 
        testFiles.image
      ));
      
      // PDF autorisé
      await assertSucceeds(uploadBytes(
        ref(userStorage, `proofs/${users.utilisateur.uid}/emprunt-1/receipt.pdf`), 
        testFiles.pdf
      ));
      
      // Fichier texte non autorisé
      await assertFails(uploadBytes(
        ref(userStorage, `proofs/${users.utilisateur.uid}/emprunt-1/note.txt`), 
        testFiles.text
      ));
    });
  });

  describe('Dossier: images/', () => {
    it('Tous les utilisateurs authentifiés peuvent lire les images', async () => {
      const adminStorage = getAuthContext(users.admin).storage();
      const userStorage = getAuthContext(users.utilisateur).storage();
      const imageRef = ref(adminStorage, 'images/equipment/camera.jpg');
      
      // Admin upload l'image
      await uploadBytes(imageRef, testFiles.image);
      
      // Utilisateur peut la lire
      const userImageRef = ref(userStorage, 'images/equipment/camera.jpg');
      await assertSucceeds(getMetadata(userImageRef));
    });

    it('Seuls admin/régisseur peuvent uploader des images', async () => {
      const userStorage = getAuthContext(users.utilisateur).storage();
      const adminStorage = getAuthContext(users.admin).storage();
      
      const imageRef = 'images/equipment/new-camera.jpg';
      
      // Utilisateur normal ne peut pas uploader
      await assertFails(uploadBytes(ref(userStorage, imageRef), testFiles.image));
      
      // Admin peut uploader
      await assertSucceeds(uploadBytes(ref(adminStorage, imageRef), testFiles.image));
    });

    it('Seules les images sont autorisées dans le dossier images', async () => {
      const adminStorage = getAuthContext(users.admin).storage();
      
      // Image autorisée
      await assertSucceeds(uploadBytes(ref(adminStorage, 'images/equipment/camera.jpg'), testFiles.image));
      
      // PDF non autorisé
      await assertFails(uploadBytes(ref(adminStorage, 'images/equipment/manual.pdf'), testFiles.pdf));
    });
  });

  describe('Dossier: admin/', () => {
    it('Seuls les admins peuvent accéder au dossier admin', async () => {
      const adminStorage = getAuthContext(users.admin).storage();
      const userStorage = getAuthContext(users.utilisateur).storage();
      const regisseurStorage = getAuthContext(users.regisseur).storage();
      
      const adminFileRef = 'admin/config.json';
      
      // Admin peut uploader
      await assertSucceeds(uploadBytes(ref(adminStorage, adminFileRef), testFiles.text));
      
      // Utilisateur ne peut pas lire
      await assertFails(getMetadata(ref(userStorage, adminFileRef)));
      
      // Régisseur ne peut pas lire
      await assertFails(getMetadata(ref(regisseurStorage, adminFileRef)));
    });

    it('Admin peut uploader n\'importe quel type de fichier dans admin/', async () => {
      const adminStorage = getAuthContext(users.admin).storage();
      
      // Différents types de fichiers autorisés pour admin
      await assertSucceeds(uploadBytes(ref(adminStorage, 'admin/config.json'), testFiles.text));
      await assertSucceeds(uploadBytes(ref(adminStorage, 'admin/backup.pdf'), testFiles.pdf));
      await assertSucceeds(uploadBytes(ref(adminStorage, 'admin/logo.jpg'), testFiles.image));
    });
  });

  describe('Dossier: exports/', () => {
    it('Admin/régisseur peuvent créer des exports', async () => {
      const adminStorage = getAuthContext(users.admin).storage();
      const regisseurStorage = getAuthContext(users.regisseur).storage();
      const userStorage = getAuthContext(users.utilisateur).storage();
      
      const exportRef = 'exports/emprunts-2024.csv';
      
      // Admin peut créer
      await assertSucceeds(uploadBytes(ref(adminStorage, exportRef), testFiles.text));
      
      // Régisseur peut créer
      await assertSucceeds(uploadBytes(ref(regisseurStorage, 'exports/stocks-2024.csv'), testFiles.text));
      
      // Utilisateur ne peut pas créer
      await assertFails(uploadBytes(ref(userStorage, 'exports/my-export.csv'), testFiles.text));
    });

    it('Tous les utilisateurs authentifiés peuvent lire les exports', async () => {
      const adminStorage = getAuthContext(users.admin).storage();
      const userStorage = getAuthContext(users.utilisateur).storage();
      const exportRef = ref(adminStorage, 'exports/public-report.csv');
      
      // Admin crée l'export
      await uploadBytes(exportRef, testFiles.text);
      
      // Utilisateur peut le lire
      const userExportRef = ref(userStorage, 'exports/public-report.csv');
      await assertSucceeds(getMetadata(userExportRef));
    });
  });

  describe('Dossier: backups/', () => {
    it('Seuls les admins peuvent accéder aux backups', async () => {
      const adminStorage = getAuthContext(users.admin).storage();
      const regisseurStorage = getAuthContext(users.regisseur).storage();
      const userStorage = getAuthContext(users.utilisateur).storage();
      
      const backupRef = 'backups/database-2024-01-01.json';
      
      // Admin peut créer
      await assertSucceeds(uploadBytes(ref(adminStorage, backupRef), testFiles.text));
      
      // Régisseur ne peut pas lire
      await assertFails(getMetadata(ref(regisseurStorage, backupRef)));
      
      // Utilisateur ne peut pas lire
      await assertFails(getMetadata(ref(userStorage, backupRef)));
    });
  });

  describe('Dossier: temp/', () => {
    it('Utilisateur peut créer des fichiers temporaires avec son UID', async () => {
      const userStorage = getAuthContext(users.utilisateur).storage();
      const tempRef = ref(userStorage, `temp/${users.utilisateur.uid}/upload.jpg`);
      
      await assertSucceeds(uploadBytes(tempRef, testFiles.image));
    });

    it('Utilisateur ne peut pas créer des fichiers temp pour d\'autres utilisateurs', async () => {
      const userStorage = getAuthContext(users.utilisateur).storage();
      const otherTempRef = ref(userStorage, `temp/${users.otherUser.uid}/upload.jpg`);
      
      await assertFails(uploadBytes(otherTempRef, testFiles.image));
    });

    it('Admin peut créer des fichiers temp pour n\'importe qui', async () => {
      const adminStorage = getAuthContext(users.admin).storage();
      const tempRef = ref(adminStorage, `temp/${users.utilisateur.uid}/admin-upload.jpg`);
      
      await assertSucceeds(uploadBytes(tempRef, testFiles.image));
    });

    it('Fichiers temp ont une limite de taille de 10MB', async () => {
      const userStorage = getAuthContext(users.utilisateur).storage();
      const tempRef = ref(userStorage, `temp/${users.utilisateur.uid}/large-file.jpg`);
      
      await assertFails(uploadBytes(tempRef, testFiles.largeFile));
    });
  });

  describe('Dossier: public/', () => {
    it('Tout le monde peut lire les fichiers publics', async () => {
      const adminStorage = getAuthContext(users.admin).storage();
      const anonStorage = getAuthContext(users.anonymous).storage();
      const publicRef = ref(adminStorage, 'public/logo.png');
      
      // Admin upload le fichier public
      await uploadBytes(publicRef, testFiles.image);
      
      // Utilisateur anonyme peut le lire
      const anonPublicRef = ref(anonStorage, 'public/logo.png');
      await assertSucceeds(getMetadata(anonPublicRef));
    });

    it('Seuls admin/régisseur peuvent uploader des fichiers publics', async () => {
      const userStorage = getAuthContext(users.utilisateur).storage();
      const adminStorage = getAuthContext(users.admin).storage();
      
      const publicRef = 'public/new-logo.png';
      
      // Utilisateur ne peut pas uploader
      await assertFails(uploadBytes(ref(userStorage, publicRef), testFiles.image));
      
      // Admin peut uploader
      await assertSucceeds(uploadBytes(ref(adminStorage, publicRef), testFiles.image));
    });
  });

  describe('Validation des fichiers', () => {
    it('Fichiers ne peuvent pas dépasser 10MB par défaut', async () => {
      const adminStorage = getAuthContext(users.admin).storage();
      const largeFileRef = ref(adminStorage, 'documents/large-manual.pdf');
      
      await assertFails(uploadBytes(largeFileRef, testFiles.largeFile));
    });

    it('Types de fichiers dangereux sont rejetés', async () => {
      const adminStorage = getAuthContext(users.admin).storage();
      
      // Fichier PHP non autorisé
      await assertFails(uploadBytes(ref(adminStorage, 'documents/script.php'), testFiles.invalidType));
      
      // Fichier exécutable non autorisé
      await assertFails(uploadBytes(ref(adminStorage, 'documents/program.exe'), testFiles.text));
    });

    it('Noms de fichiers avec caractères spéciaux sont rejetés', async () => {
      const userStorage = getAuthContext(users.utilisateur).storage();
      
      // Caractères spéciaux dans le nom
      await assertFails(uploadBytes(
        ref(userStorage, `avatars/${users.utilisateur.uid}/../../../etc/passwd`), 
        testFiles.image
      ));
    });
  });

  describe('Suppression de fichiers', () => {
    it('Utilisateur peut supprimer ses propres fichiers', async () => {
      const userStorage = getAuthContext(users.utilisateur).storage();
      const avatarRef = ref(userStorage, `avatars/${users.utilisateur.uid}/profile.jpg`);
      
      // Upload puis suppression
      await uploadBytes(avatarRef, testFiles.image);
      await assertSucceeds(deleteObject(avatarRef));
    });

    it('Utilisateur ne peut pas supprimer les fichiers d\'autres utilisateurs', async () => {
      const userStorage = getAuthContext(users.utilisateur).storage();
      const adminStorage = getAuthContext(users.admin).storage();
      
      // Admin upload un fichier
      const otherAvatarRef = ref(adminStorage, `avatars/${users.otherUser.uid}/profile.jpg`);
      await uploadBytes(otherAvatarRef, testFiles.image);
      
      // Utilisateur ne peut pas le supprimer
      const userOtherAvatarRef = ref(userStorage, `avatars/${users.otherUser.uid}/profile.jpg`);
      await assertFails(deleteObject(userOtherAvatarRef));
    });

    it('Admin peut supprimer n\'importe quel fichier', async () => {
      const userStorage = getAuthContext(users.utilisateur).storage();
      const adminStorage = getAuthContext(users.admin).storage();
      
      // Utilisateur upload un fichier
      const userAvatarRef = ref(userStorage, `avatars/${users.utilisateur.uid}/profile.jpg`);
      await uploadBytes(userAvatarRef, testFiles.image);
      
      // Admin peut le supprimer
      const adminUserAvatarRef = ref(adminStorage, `avatars/${users.utilisateur.uid}/profile.jpg`);
      await assertSucceeds(deleteObject(adminUserAvatarRef));
    });
  });
});
